{"enabled": true, "name": "Java Documentation Sync", "description": "Monitors Java source files, configuration files, and database migrations for changes and automatically updates project documentation in README or docs folder to keep it synchronized with code changes.", "version": "1", "when": {"type": "userTriggered", "patterns": ["src/main/java/**/*.java", "src/test/java/**/*.java", "src/main/resources/**/*.properties", "src/main/resources/db/migration/*.sql", "pom.xml"]}, "then": {"type": "askAgent", "prompt": "Source code files have been modified in this Java Spring Boot authentication service. Please analyze the changes and update the project documentation accordingly. Focus on:\n\n1. If Java classes were modified, update API documentation, architectural descriptions, or feature descriptions in README.md\n2. If configuration files (.properties) were changed, update configuration documentation\n3. If database migrations were added/modified, update database schema documentation\n4. If pom.xml was changed, update dependency or build documentation\n5. If test files were modified, update testing documentation if relevant\n\nEnsure the documentation accurately reflects the current state of the codebase, including any new features, API changes, configuration options, or architectural modifications. Update either the README.md file or create/update files in a /docs folder if one exists."}}