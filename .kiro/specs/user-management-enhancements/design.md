# Documento de Diseño - Mejoras de Gestión de Usuarios

## Resumen

Este documento describe el diseño para las mejoras del sistema de gestión de usuarios que incluye generación automática de nombres de usuario, inicio de sesión flexible con email o nombre de usuario, mejor manejo de usuarios duplicados y eliminación lógica. Las mejoras se integrarán con la arquitectura existente de Spring Boot manteniendo la compatibilidad hacia atrás y siguiendo los patrones establecidos.

## Arquitectura

### Cambios en la Capa de Entidad

#### Modificaciones a la Entidad User
- **Nuevo campo `username`**: Se agregará un campo único para almacenar nombres de usuario generados automáticamente
- **Nuevo campo `status`**: Se reemplazará el campo booleano `enabled` con un enum `UserStatus` para soportar eliminación lógica
- **Índices de base de datos**: Se agregarán índices para `username` y `status` para optimizar consultas

**Decisión de diseño**: Se mantiene la estructura existente agregando campos en lugar de modificar para minimizar el impacto en el código existente.

#### Nuevo Enum UserStatus
```java
public enum UserStatus {
    ACTIVE,    // Usuario activo (equivale a enabled=true)
    INACTIVE,  // Usuario inactivo (equivale a enabled=false)
    DELETED    // Usuario eliminado lógicamente
}
```

**Rationale**: Un enum proporciona mejor expresividad que un booleano y permite futuras extensiones de estados.

### Cambios en la Capa de Repositorio

#### Modificaciones a UserRepository
- **Nuevo método `findByUsername`**: Para búsqueda por nombre de usuario
- **Nuevo método `findByUsernameOrEmail`**: Para login flexible
- **Modificación de métodos existentes**: Actualizar consultas para considerar `UserStatus` en lugar de `enabled`
- **Nuevos métodos de validación**: Para verificar unicidad incluyendo usuarios eliminados

**Decisión de diseño**: Se mantienen los métodos existentes con comportamiento compatible, agregando nuevos métodos para funcionalidad adicional.

### Cambios en la Capa de Servicio

#### Generador de Nombres de Usuario
Se implementará una nueva clase `UsernameGenerator` como componente Spring:

```java
@Component
public class UsernameGenerator {
    public String generateUsername(String firstName, String lastName, UserRepository userRepository)
}
```

**Algoritmo de generación**:
1. Tomar primera inicial del firstName en minúsculas
2. Concatenar con lastName completo en minúsculas
3. Remover espacios y caracteres especiales
4. Verificar unicidad contra todos los usuarios (incluyendo eliminados)
5. Si existe, agregar número incremental (2, 3, 4...)

**Rationale**: Componente separado permite fácil testing y reutilización. La verificación incluye usuarios eliminados para evitar confusión.

#### Modificaciones a UserService
- **Método `createUser` mejorado**: Integrar generación automática de username
- **Nuevo método `softDeleteUser`**: Para eliminación lógica
- **Modificación de validaciones**: Incluir usuarios eliminados en verificaciones de duplicados
- **Mejores mensajes de error**: Mensajes específicos para diferentes tipos de conflictos

#### Modificaciones a AuthenticationService
- **Login flexible**: Modificar lógica de autenticación para aceptar username o email
- **Validación de estado**: Verificar que el usuario no esté en estado DELETED

**Decisión de diseño**: Se mantiene la interfaz pública existente, extendiendo funcionalidad internamente.

## Componentes e Interfaces

### Nuevos Componentes

#### UsernameGenerator
```java
@Component
public class UsernameGenerator {
    public String generateUsername(String firstName, String lastName, UserRepository userRepository);
    private String sanitizeString(String input);
    private String generateBaseUsername(String firstName, String lastName);
    private String findAvailableUsername(String baseUsername, UserRepository userRepository);
}
```

#### UserStatusService (Opcional)
```java
@Service
public class UserStatusService {
    public void softDeleteUser(Long userId);
    public void reactivateUser(Long userId);
    public boolean isUserActive(User user);
}
```

### Interfaces Modificadas

#### UserRepository (Extensiones)
```java
public interface UserRepository extends JpaRepository<User, Long> {
    // Métodos existentes...
    
    // Nuevos métodos
    Optional<User> findByUsername(String username);
    Optional<User> findByUsernameOrEmail(String usernameOrEmail, String email);
    boolean existsByUsername(String username);
    boolean existsByUsernameIncludingDeleted(String username);
    boolean existsByEmailIncludingDeleted(String email);
    Page<User> findByStatus(UserStatus status, Pageable pageable);
}
```

## Modelos de Datos

### Migración de Base de Datos

#### Nueva migración V7__Add_username_and_status_fields.sql
```sql
-- Agregar campo username
ALTER TABLE users ADD COLUMN username VARCHAR(100) UNIQUE;

-- Agregar campo status
ALTER TABLE users ADD COLUMN status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE';

-- Migrar datos existentes
UPDATE users SET status = 'ACTIVE' WHERE enabled = true;
UPDATE users SET status = 'INACTIVE' WHERE enabled = false;

-- Generar usernames para usuarios existentes
-- (Se implementará lógica en Java para esto)

-- Agregar índices
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_status ON users(status);

-- Hacer username NOT NULL después de la migración de datos
ALTER TABLE users ALTER COLUMN username SET NOT NULL;
```

**Decisión de diseño**: Se mantiene el campo `enabled` temporalmente para compatibilidad durante la migración, se eliminará en una migración posterior.

### Estructura de Datos Actualizada

#### Entidad User (Campos nuevos/modificados)
```java
@Column(name = "username", unique = true, nullable = false, length = 100)
private String username;

@Enumerated(EnumType.STRING)
@Column(name = "status", nullable = false, length = 20)
private UserStatus status = UserStatus.ACTIVE;

// Método de conveniencia
public boolean isActive() {
    return status == UserStatus.ACTIVE;
}
```

## Manejo de Errores

### Nuevas Excepciones
```java
public class UsernameAlreadyExistsException extends RuntimeException
public class UserDeletedException extends RuntimeException
```

### Mensajes de Error Mejorados
- **Email duplicado**: "El email ya está registrado en el sistema"
- **Usuario eliminado**: Tratado como "usuario no encontrado" por seguridad
- **Credenciales inválidas**: Mensaje genérico para username/email no encontrado

**Decisión de diseño**: No revelar si un email pertenece a un usuario eliminado por razones de seguridad.

## Estrategia de Testing

### Tests Unitarios
- **UsernameGeneratorTest**: Verificar algoritmo de generación y manejo de duplicados
- **UserServiceTest**: Nuevos métodos y modificaciones a existentes
- **AuthenticationServiceTest**: Login flexible con username/email

### Tests de Integración
- **UserRepositoryTest**: Nuevos métodos de consulta
- **AuthenticationControllerIntegrationTest**: Flujos completos de login
- **DatabaseIntegrationTest**: Migración de datos y consultas con UserStatus

### Tests de Migración
- **UserDataMigrationTest**: Verificar migración correcta de datos existentes
- **UsernameGenerationMigrationTest**: Verificar generación de usernames para usuarios existentes

**Decisión de diseño**: Tests exhaustivos para asegurar compatibilidad hacia atrás y correcta migración de datos.

## Consideraciones de Implementación

### Migración de Datos Existentes
1. **Fase 1**: Agregar nuevos campos con valores por defecto
2. **Fase 2**: Ejecutar script de migración de datos para generar usernames
3. **Fase 3**: Hacer campos obligatorios y agregar restricciones
4. **Fase 4**: (Futuro) Remover campo `enabled` obsoleto

### Compatibilidad hacia Atrás
- Los endpoints existentes continuarán funcionando
- El campo `enabled` se mantendrá temporalmente como computed property
- Las consultas existentes se actualizarán gradualmente

### Rendimiento
- Índices en `username` y `status` para optimizar consultas frecuentes
- Consultas optimizadas para evitar N+1 problems
- Cache de usernames generados durante migración masiva

### Seguridad
- Validación de entrada para prevenir inyección
- No exposición de información sobre usuarios eliminados
- Logging de eventos de seguridad para auditoría

## Flujos de Trabajo Actualizados

### Registro de Usuario
1. Validar datos de entrada (firstName, lastName, email, password)
2. Verificar que email no exista (incluyendo usuarios eliminados)
3. Generar username único basado en firstName + lastName
4. Crear usuario con status ACTIVE
5. Retornar respuesta con username generado

### Login Flexible
1. Recibir identificador (puede ser username o email)
2. Intentar buscar por username primero
3. Si no se encuentra, buscar por email
4. Verificar que usuario esté en status ACTIVE
5. Validar password y proceder con autenticación normal

### Eliminación Lógica
1. Verificar que usuario existe y no está ya eliminado
2. Cambiar status a DELETED
3. Invalidar todas las sesiones activas
4. Invalidar todos los refresh tokens
5. Log del evento para auditoría

**Decisión de diseño**: Flujos diseñados para mantener compatibilidad mientras agregan nueva funcionalidad de manera transparente.