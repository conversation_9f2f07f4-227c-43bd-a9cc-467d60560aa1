# Documento de Requerimientos

## Introducción

Esta funcionalidad mejora el sistema de gestión de usuarios existente con generación automática de nombres de usuario, opciones flexibles de inicio de sesión, mejor manejo de usuarios duplicados y capacidades de eliminación lógica. Las mejoras mejorarán la experiencia del usuario mientras mantienen la integridad de los datos y soportan una mejor gestión del ciclo de vida del usuario.

## Requerimientos

### Requerimiento 1: Nombre de Usuario Auto-generado Durante el Registro

**Historia de Usuario:** Como usuario que se registra en el sistema, quiero que se genere automáticamente un nombre de usuario a partir de mi nombre, para tener un identificador único sin tener que pensar en uno yo mismo.

#### Criterios de Aceptación

1. CUANDO un usuario se registra con firstName y lastName ENTONCES el sistema DEBE generar un nombre de usuario usando la primera inicial del firstName y el lastName completo en minúsculas
2. CUANDO el nombre de usuario generado ya existe (incluyendo usuarios inactivos/eliminados) ENTONCES el sistema DEBE agregar un número incremental comenzando desde 2
3. CUANDO se generen nombres de usuario ENTONCES el sistema DEBE usar el formato: inicial_del_nombre + apellido (ej., "Jose Luis" + "Marin Arango" = "jmarin")
4. CUANDO se verifiquen nombres de usuario existentes ENTONCES el sistema DEBE incluir tanto usuarios activos como inactivos/eliminados en la validación
5. CUANDO múltiples usuarios tendrían el mismo nombre de usuario base ENTONCES el sistema DEBE asignar números secuenciales (jmarin, jmarin2, jmarin3, etc.)

### Requerimiento 2: Inicio de Sesión con Nombre de Usuario o Email

**Historia de Usuario:** Como usuario registrado, quiero iniciar sesión usando mi nombre de usuario o dirección de email, para tener flexibilidad en cómo accedo a mi cuenta.

#### Criterios de Aceptación

1. CUANDO un usuario intenta iniciar sesión ENTONCES el sistema DEBE aceptar tanto el nombre de usuario como el email como identificador de login
2. CUANDO se proporciona el identificador de login ENTONCES el sistema DEBE primero verificar si coincide con un nombre de usuario, luego verificar si coincide con un email
3. CUANDO la autenticación es exitosa con cualquier identificador ENTONCES el sistema DEBE retornar el mismo formato de respuesta de autenticación
4. CUANDO el identificador no coincide con ningún usuario activo ENTONCES el sistema DEBE retornar un mensaje apropiado de falla de autenticación
5. CUANDO se validen credenciales ENTONCES el sistema DEBE considerar solo usuarios activos (no eliminados) para la autenticación

### Requerimiento 3: Mensaje Mejorado para Usuarios Duplicados

**Historia de Usuario:** Como administrador del sistema, quiero mensajes claros cuando se detecten usuarios duplicados, para entender por qué falló el registro y tomar la acción apropiada.

#### Criterios de Aceptación

1. CUANDO un usuario intenta registrarse con un email que ya existe ENTONCES el sistema DEBE retornar un mensaje de error específico "email ya existe"
2. CUANDO se verifiquen emails duplicados ENTONCES el sistema DEBE incluir tanto usuarios activos como inactivos/eliminados en la validación
3. CUANDO se encuentre un email duplicado ENTONCES el sistema NO DEBE revelar si el usuario existente está activo o eliminado por razones de seguridad
4. CUANDO ocurra el error ENTONCES el sistema DEBE retornar estado HTTP 409 Conflict con detalles descriptivos del error
5. CUANDO se valide unicidad ENTONCES el sistema DEBE realizar comparación de email insensible a mayúsculas/minúsculas

### Requerimiento 4: Implementación de Eliminación Lógica

**Historia de Usuario:** Como administrador del sistema, quiero que los usuarios sean eliminados lógicamente en lugar de ser removidos físicamente, para mantener la integridad de los datos y pistas de auditoría mientras prevengo el acceso.

#### Criterios de Aceptación

1. CUANDO un usuario es eliminado ENTONCES el sistema DEBE establecer el estado del usuario a DELETED en lugar de remover el registro
2. CUANDO se consulten usuarios activos ENTONCES el sistema DEBE excluir usuarios con estado DELETED de los resultados
3. CUANDO se validen duplicados ENTONCES el sistema DEBE incluir usuarios DELETED en las verificaciones de unicidad de email y nombre de usuario
4. CUANDO un usuario DELETED intenta iniciar sesión ENTONCES el sistema DEBE tratarlo como si no existiera
5. CUANDO se listen usuarios para propósitos administrativos ENTONCES el sistema DEBE proporcionar opciones para incluir o excluir usuarios DELETED
6. CUANDO un usuario es marcado como DELETED ENTONCES el sistema DEBE invalidar todas sus sesiones activas y tokens de actualización
7. CUANDO se implemente el estado de usuario ENTONCES el sistema DEBE soportar como mínimo los estados: ACTIVE, INACTIVE, DELETED