# GEDSYS Authentication Service - API Documentation

## Overview

This document provides comprehensive information about all available REST API endpoints in the GEDSYS Authentication Service. The service provides JWT-based authentication, user management, and push token management capabilities.

## Security Configuration

The service implements a comprehensive security configuration with the following features:

### JWT Service Testing
The JWT service includes extensive test coverage ensuring reliability and security:
- **Token Generation**: Validates proper token creation for different session types (mobile/web)
- **Token Validation**: Tests signature verification, expiration checks, and user validation
- **Claim Extraction**: Verifies extraction of username, user ID, role, and session type
- **Edge Case Handling**: Tests malformed tokens, tampered signatures, and null values
- **Session Type Awareness**: Validates different expiration times for mobile vs web sessions
- **Error Handling**: Ensures proper exception handling for security violations

### JWT Authentication Filter
- Custom JWT authentication filter processes all requests
- Extracts and validates JWT tokens from Authorization header
- Sets security context for authenticated requests

### CORS Configuration
- Configurable cross-origin resource sharing
- Supports localhost development environments
- Allows credentials and exposes necessary headers
- Caches preflight responses for performance

### Endpoint Security
- Public endpoints: `/auth/login`, `/auth/refresh`, health checks
- Protected endpoints: All `/auth/**` endpoints require authentication
- Admin endpoints: All `/admin/**` endpoints require ADMIN role
- Method-level security with `@PreAuthorize` annotations

### Session Management
- Stateless session creation policy
- Custom authentication entry point for 401 errors
- Automatic session invalidation on security events

## Base URL
```
http://localhost:8080
```

## Authentication
Most endpoints require JWT authentication via the `Authorization` header:
```
Authorization: Bearer <jwt-token>
```

## Response Format
All responses follow a consistent JSON format:

### Success Response
```json
{
  "data": { ... },
  "message": "Operation successful"
}
```

### Error Response
```json
{
  "error": "Error description",
  "timestamp": "2024-01-01T12:00:00Z",
  "path": "/api/endpoint"
}
```

## New Features

### Username Generation
The service now automatically generates unique usernames for all users based on their first and last names:

**Algorithm:**
1. Take first initial of firstName (lowercase)
2. Concatenate with full lastName (lowercase)
3. Remove diacritics, spaces, and special characters
4. Check uniqueness against all users (including deleted ones)
5. Add incremental numbers for duplicates (e.g., "jdoe", "jdoe2", "jdoe3")

**Examples:**
- John Doe → "jdoe"
- Jane Smith → "jsmith"
- José García → "jgarcia"
- John Doe (duplicate) → "jdoe2"

### Flexible Authentication
Users can now log in using either their username or email address:
- **Username**: Auto-generated unique identifier (e.g., "jdoe")
- **Email**: Traditional email-based authentication
- **Backward Compatibility**: Existing API clients continue to work

### User Status Management
Enhanced user lifecycle management with logical deletion:
- **ACTIVE**: User can access the system (default)
- **INACTIVE**: User is disabled but not deleted
- **DELETED**: User is logically deleted (data preserved for audit)

## Endpoints

### 🔐 Authentication Endpoints

#### POST /auth/login
Authenticate user and create session.

**Request Body:**
```json
{
  "identifier": "<EMAIL>",   // Username or email address
  "password": "password123",
  "sessionType": "MOBILE|WEB",
  "pushToken": "fcm-token-here",      // Optional, mobile only
  "deviceType": "IOS|ANDROID",        // Optional, mobile only
  "deviceId": "device-uuid"           // Optional, mobile only
}
```

**Flexible Authentication:**
- **identifier**: Accepts either username (e.g., "jdoe") or email address
- **Backward Compatibility**: The deprecated `email` field is still supported via getter/setter methods in LoginRequest
- **Username Format**: Auto-generated usernames follow the pattern: first initial + last name (e.g., "jsmith", "jdoe2")
- **Sequential Search**: Authentication service searches first by username, then by email if not found

**Response:**
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIs...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
  "tokenType": "Bearer",
  "expiresIn": 3600,
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "USER",
    "enabled": true,
    "createdAt": "2024-01-01T12:00:00Z"
  }
}
```

**Session Type Behavior:**
- **WEB**: Creates web session with standard token expiration
- **MOBILE**: Creates mobile session with extended token expiration and optional push token registration

**Status Codes:**
- `200 OK` - Login successful
- `401 Unauthorized` - Invalid credentials
- `403 Forbidden` - Account disabled
- `400 Bad Request` - Invalid request format

**Testing Coverage:**
- ✅ Web session login with token validation
- ✅ Mobile session login with push token registration
- ✅ Flexible login with username or email
- ✅ Invalid credentials handling
- ✅ Session type-specific token storage
- ✅ User profile inclusion in response with username

---

#### POST /auth/refresh
Refresh access token using refresh token.

**Request Body:**
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
}
```

**Response:**
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIs...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
  "tokenType": "Bearer",
  "expiresIn": 3600
}
```

**Single-Use Policy:**
- Each refresh token can only be used once
- After successful use, the old refresh token is invalidated
- A new refresh token is issued with each refresh operation
- Attempting to reuse a refresh token will result in 401 Unauthorized

**Status Codes:**
- `200 OK` - Token refreshed successfully
- `401 Unauthorized` - Invalid or expired refresh token
- `400 Bad Request` - Invalid request format

**Testing Coverage:**
- ✅ Successful token refresh with new tokens
- ✅ Single-use enforcement (second use fails)
- ✅ Token rotation validation
- ✅ Invalid token handling

---

#### POST /auth/logout
Logout user and invalidate tokens.

**Headers:**
```
Authorization: Bearer <jwt-token>
```

**Request Body:**
```json
{
  "sessionType": "MOBILE|WEB"  // Optional
}
```

**Response:**
```json
{
  "message": "Logout successful"
}
```

**Session-Specific Behavior:**
- **WEB**: Invalidates web refresh token (marks as used)
- **MOBILE**: Invalidates mobile refresh token and removes push token

**Status Codes:**
- `200 OK` - Logout successful
- `401 Unauthorized` - Invalid token
- `400 Bad Request` - Invalid session type

**Testing Coverage:**
- ✅ Web session logout with token invalidation
- ✅ Mobile session logout with push token cleanup
- ✅ Proper token cleanup verification
- ✅ Session-specific logout behavior

---

#### GET /auth/profile
Get current user profile.

**Headers:**
```
Authorization: Bearer <jwt-token>
```

**Response:**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "username": "jdoe",
  "firstName": "John",
  "lastName": "Doe",
  "role": "USER",
  "enabled": true,
  "createdAt": "2024-01-01T12:00:00Z",
  "updatedAt": "2024-01-01T12:00:00Z"
}
```

**Status Codes:**
- `200 OK` - Profile retrieved successfully
- `401 Unauthorized` - Invalid token

---

#### PUT /auth/change-password
Change user password.

**Headers:**
```
Authorization: Bearer <jwt-token>
```

**Request Body:**
```json
{
  "currentPassword": "oldPassword123",
  "newPassword": "newPassword456",
  "confirmPassword": "newPassword456"
}
```

**Response:**
```json
{
  "message": "Password changed successfully. Please login again."
}
```

**Security Behavior:**
- All active sessions (web and mobile) are invalidated after password change
- User must re-authenticate with new password
- Old password becomes invalid immediately
- All refresh tokens are marked as used

**Validation Rules:**
- Current password must be correct
- New password must be different from current password
- New password and confirmation must match

**Status Codes:**
- `200 OK` - Password changed successfully
- `400 Bad Request` - Password validation failed
- `401 Unauthorized` - Invalid current password
- `422 Unprocessable Entity` - Password confirmation mismatch

**Testing Coverage:**
- ✅ Successful password change with session invalidation
- ✅ Wrong current password handling
- ✅ Password confirmation mismatch validation
- ✅ Same password rejection
- ✅ All sessions invalidation verification
- ✅ New password authentication validation

---

#### GET /auth/sessions
Get active sessions information.

**Headers:**
```
Authorization: Bearer <jwt-token>
```

**Response:**
```json
{
  "hasMobileSession": true,
  "hasWebSession": false,
  "totalActiveSessions": 1
}
```

**Session Management:**
- Tracks active sessions per session type (mobile/web)
- Only one session per type is allowed (single session per type policy)
- New login invalidates previous session of same type

**Status Codes:**
- `200 OK` - Session info retrieved successfully
- `401 Unauthorized` - Invalid token

**Testing Coverage:**
- ✅ Active session tracking for multiple session types
- ✅ Session count validation
- ✅ Single session per type enforcement

---

#### GET /auth/validate
Validate current JWT token.

**Headers:**
```
Authorization: Bearer <jwt-token>
```

**Response:**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "username": "jdoe",
  "firstName": "John",
  "lastName": "Doe",
  "role": "USER",
  "enabled": true,
  "createdAt": "2024-01-01T12:00:00Z"
}
```

**Token Validation:**
- Verifies JWT signature and expiration
- Returns user profile if token is valid
- Can be used for token health checks

**Status Codes:**
- `200 OK` - Token is valid
- `401 Unauthorized` - Invalid or expired token

**Testing Coverage:**
- ✅ Valid token validation with user profile return
- ✅ Invalid token handling

### 📱 Push Token Endpoints

#### POST /auth/push-token
Register or update push token (Mobile sessions only).

**Headers:**
```
Authorization: Bearer <mobile-jwt-token>
```

**Request Body:**
```json
{
  "pushToken": "fcm-token-here",
  "deviceType": "IOS|ANDROID",
  "deviceId": "device-uuid"
}
```

**Response:**
```json
{
  "message": "Push token registered successfully",
  "pushToken": {
    "id": 1,
    "pushToken": "fcm-token-here",
    "deviceType": "IOS",
    "deviceId": "device-uuid",
    "createdAt": "2024-01-01T12:00:00Z",
    "lastUsedAt": "2024-01-01T12:00:00Z"
  }
}
```

**Status Codes:**
- `200 OK` - Push token registered successfully
- `403 Forbidden` - Not a mobile session
- `400 Bad Request` - Invalid device type or token

---

#### GET /auth/push-token
Get current push token (Mobile sessions only).

**Headers:**
```
Authorization: Bearer <mobile-jwt-token>
```

**Response:**
```json
{
  "message": "Push token retrieved successfully",
  "pushToken": {
    "id": 1,
    "pushToken": "fcm-token-here",
    "deviceType": "IOS",
    "deviceId": "device-uuid",
    "createdAt": "2024-01-01T12:00:00Z",
    "lastUsedAt": "2024-01-01T12:00:00Z"
  }
}
```

**Status Codes:**
- `200 OK` - Push token retrieved successfully
- `403 Forbidden` - Not a mobile session
- `404 Not Found` - No push token registered

---

#### PUT /auth/push-token
Update existing push token (Mobile sessions only).

**Headers:**
```
Authorization: Bearer <mobile-jwt-token>
```

**Request Body:**
```json
{
  "pushToken": "new-fcm-token-here",
  "deviceType": "ANDROID",
  "deviceId": "new-device-uuid"
}
```

**Response:**
```json
{
  "message": "Push token updated successfully",
  "pushToken": {
    "id": 1,
    "pushToken": "new-fcm-token-here",
    "deviceType": "ANDROID",
    "deviceId": "new-device-uuid",
    "createdAt": "2024-01-01T12:00:00Z",
    "lastUsedAt": "2024-01-01T13:00:00Z"
  }
}
```

**Status Codes:**
- `200 OK` - Push token updated successfully
- `403 Forbidden` - Not a mobile session
- `400 Bad Request` - No existing token to update

---

#### DELETE /auth/push-token
Delete push token (Mobile sessions only).

**Headers:**
```
Authorization: Bearer <mobile-jwt-token>
```

**Response:**
```json
{
  "message": "Push token deleted successfully"
}
```

**Status Codes:**
- `200 OK` - Push token deleted successfully
- `403 Forbidden` - Not a mobile session

---

#### GET /auth/push-token/exists
Check if user has push token (Mobile sessions only).

**Headers:**
```
Authorization: Bearer <mobile-jwt-token>
```

**Response:**
```json
{
  "hasPushToken": true,
  "message": "User has push token"
}
```

**Status Codes:**
- `200 OK` - Check completed successfully
- `403 Forbidden` - Not a mobile session

### 👑 Admin Endpoints

All admin endpoints require `ADMIN` role.

#### GET /admin/users
Get paginated list of users with optional filters.

**Headers:**
```
Authorization: Bearer <admin-jwt-token>
```

**Query Parameters:**
- `email` (optional) - Filter by email (partial match)
- `enabled` (optional) - Filter by enabled status (true/false)
- `role` (optional) - Filter by role (USER/ADMIN)
- `page` (optional, default: 0) - Page number
- `size` (optional, default: 20) - Page size (max: 100)
- `sort` (optional, default: "createdAt,desc") - Sort criteria

**Example:**
```
GET /admin/users?email=john&enabled=true&role=USER&page=0&size=10&sort=email,asc
```

**Response:**
```json
{
  "content": [
    {
      "id": 1,
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "role": "USER",
      "enabled": true,
      "createdAt": "2024-01-01T12:00:00Z",
      "updatedAt": "2024-01-01T12:00:00Z"
    }
  ],
  "pageable": {
    "page": 0,
    "size": 10,
    "sort": "email,asc"
  },
  "totalElements": 1,
  "totalPages": 1,
  "first": true,
  "last": true,
  "empty": false
}
```

**Status Codes:**
- `200 OK` - Users retrieved successfully
- `403 Forbidden` - Not an admin user
- `400 Bad Request` - Invalid pagination parameters

---

#### GET /admin/users/{userId}
Get detailed user information by ID.

**Headers:**
```
Authorization: Bearer <admin-jwt-token>
```

**Response:**
```json
{
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "username": "jdoe",
    "firstName": "John",
    "lastName": "Doe",
    "role": "USER",
    "enabled": true,
    "createdAt": "2024-01-01T12:00:00Z",
    "updatedAt": "2024-01-01T12:00:00Z"
  },
  "activeSessions": [
    {
      "sessionType": "MOBILE",
      "lastActivity": "2024-01-01T12:00:00Z",
      "expiresAt": "2024-01-08T12:00:00Z"
    }
  ],
  "pushToken": {
    "id": 1,
    "deviceType": "IOS",
    "deviceId": "device-uuid",
    "createdAt": "2024-01-01T12:00:00Z",
    "lastUsedAt": "2024-01-01T12:00:00Z"
  }
}
```

**Status Codes:**
- `200 OK` - User details retrieved successfully
- `403 Forbidden` - Not an admin user
- `404 Not Found` - User not found

---

#### POST /admin/users
Create a new user.

**Headers:**
```
Authorization: Bearer <admin-jwt-token>
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "Jane",
  "lastName": "Smith",
  "role": "USER",
  "enabled": true
}
```

**Response:**
```json
{
  "id": 2,
  "email": "<EMAIL>",
  "username": "jsmith",
  "firstName": "Jane",
  "lastName": "Smith",
  "role": "USER",
  "enabled": true,
  "createdAt": "2024-01-01T12:00:00Z",
  "updatedAt": "2024-01-01T12:00:00Z"
}
```

**Username Generation:**
- Username is automatically generated from firstName and lastName
- If a duplicate exists, incremental numbers are added (e.g., "jsmith2")
- Username can also be explicitly provided in the request (must be unique)

**Status Codes:**
- `201 Created` - User created successfully
- `403 Forbidden` - Not an admin user
- `409 Conflict` - Email already exists
- `400 Bad Request` - Invalid user data

---

#### PUT /admin/users/{userId}
Update an existing user.

**Headers:**
```
Authorization: Bearer <admin-jwt-token>
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "newPassword123",  // Optional
  "firstName": "Jane",
  "lastName": "Doe",
  "role": "ADMIN",
  "enabled": false
}
```

**Response:**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "firstName": "Jane",
  "lastName": "Doe",
  "role": "ADMIN",
  "enabled": false,
  "createdAt": "2024-01-01T12:00:00Z",
  "updatedAt": "2024-01-01T13:00:00Z"
}
```

**Status Codes:**
- `200 OK` - User updated successfully
- `403 Forbidden` - Not an admin user
- `404 Not Found` - User not found
- `409 Conflict` - Email already exists
- `400 Bad Request` - Invalid user data

---

#### DELETE /admin/users/{userId}
Delete a user.

**Headers:**
```
Authorization: Bearer <admin-jwt-token>
```

**Response:**
```json
{
  "message": "User deleted successfully"
}
```

**Status Codes:**
- `200 OK` - User deleted successfully
- `403 Forbidden` - Not an admin user
- `404 Not Found` - User not found

---

#### PATCH /admin/users/{userId}/enabled
Enable or disable a user.

**Headers:**
```
Authorization: Bearer <admin-jwt-token>
```

**Request Body:**
```json
{
  "enabled": false
}
```

**Response:**
```json
{
  "message": "User disabled successfully",
  "enabled": false
}
```

**Status Codes:**
- `200 OK` - User status changed successfully
- `403 Forbidden` - Not an admin user
- `404 Not Found` - User not found
- `400 Bad Request` - Invalid enabled status

---

#### POST /admin/users/{userId}/invalidate-sessions
Invalidate all sessions for a user.

**Headers:**
```
Authorization: Bearer <admin-jwt-token>
```

**Response:**
```json
{
  "message": "All user sessions invalidated successfully"
}
```

**Status Codes:**
- `200 OK` - Sessions invalidated successfully
- `403 Forbidden` - Not an admin user
- `404 Not Found` - User not found

---

#### GET /admin/users/stats
Get user statistics.

**Headers:**
```
Authorization: Bearer <admin-jwt-token>
```

**Response:**
```json
{
  "totalUsers": 150,
  "enabledUsers": 140,
  "disabledUsers": 10,
  "adminUsers": 5,
  "regularUsers": 145,
  "totalPushTokens": 85
}
```

**Status Codes:**
- `200 OK` - Statistics retrieved successfully
- `403 Forbidden` - Not an admin user

## Exception Handling

The service implements a comprehensive global exception handling system that provides consistent error responses across all endpoints.

### Exception Classes

The following custom exceptions are defined for domain-specific error handling:

- **`InvalidCredentialsException`** - Thrown when login credentials are invalid
- **`TokenExpiredException`** - Thrown when JWT or refresh tokens have expired
- **`InvalidTokenException`** - Thrown when tokens are malformed or invalid
- **`UserNotFoundException`** - Thrown when a user cannot be found
- **`EmailAlreadyExistsException`** - Thrown when attempting to create a user with an existing email

### Error Response Format

All errors return a consistent JSON structure:

```json
{
  "error": "ERROR_CODE",
  "message": "Human-readable error description",
  "status": 400,
  "timestamp": "2024-01-01T12:00:00",
  "path": "/api/endpoint",
  "validationErrors": {
    "field": "error message"
  }
}
```

### Security Event Logging

The system logs security-related events for monitoring and audit purposes:
- Authentication failures with client IP addresses
- Token expiration events
- Access denied attempts
- Invalid token usage
- User creation attempts with existing emails

## Error Codes

### Common HTTP Status Codes
- `200 OK` - Request successful
- `201 Created` - Resource created successfully
- `400 Bad Request` - Invalid request format or parameters
- `401 Unauthorized` - Authentication required or invalid
- `403 Forbidden` - Access denied (insufficient permissions)
- `404 Not Found` - Resource not found
- `409 Conflict` - Resource conflict (e.g., email already exists)
- `422 Unprocessable Entity` - Validation errors
- `500 Internal Server Error` - Server error

### Custom Error Messages
- `INVALID_CREDENTIALS` - Invalid username/email or password
- `ACCOUNT_DISABLED` - User account is disabled
- `TOKEN_EXPIRED` - JWT token or refresh token has expired
- `INVALID_TOKEN` - JWT token is invalid or malformed
- `EMAIL_ALREADY_EXISTS` - Email address is already registered (including deleted users)
- `USERNAME_ALREADY_EXISTS` - Username is already taken (including deleted users)
- `USER_NOT_FOUND` - User does not exist
- `USER_DELETED` - User has been logically deleted
- `INVALID_SESSION_TYPE` - Invalid session type provided
- `MOBILE_SESSION_REQUIRED` - Operation requires mobile session
- `ADMIN_ROLE_REQUIRED` - Operation requires admin privileges

### Enhanced Validation
- **Email Uniqueness**: Checks against all users including deleted ones
- **Username Uniqueness**: Prevents conflicts with deleted user usernames
- **Flexible Login**: Validates both username and email formats
- **Status Validation**: Prevents access for deleted users


## Integration Testing

The authentication service includes comprehensive integration tests that validate complete end-to-end flows using real database containers and HTTP requests.

### Authentication Controller Integration Tests

The `AuthenticationControllerIntegrationTest` class provides extensive coverage of authentication flows:

#### Test Coverage Areas

**Login Functionality:**
- ✅ Web session login with token and user profile validation
- ✅ Mobile session login with push token registration
- ✅ Invalid credentials handling with proper error responses
- ✅ Session-specific token storage verification

**Token Management:**
- ✅ Refresh token functionality with new token generation
- ✅ Single-use refresh token enforcement (prevents token reuse)
- ✅ Token rotation validation (new tokens differ from old ones)
- ✅ Invalid refresh token handling

**Logout Operations:**
- ✅ Web session logout with refresh token invalidation
- ✅ Mobile session logout with push token cleanup
- ✅ Proper token cleanup verification in database
- ✅ Session-specific logout behavior validation

**Profile Management:**
- ✅ User profile retrieval with authentication
- ✅ Unauthorized access prevention without valid tokens
- ✅ Profile data accuracy validation

**Password Management:**
- ✅ Successful password change with all session invalidation
- ✅ Wrong current password rejection
- ✅ Password confirmation mismatch validation
- ✅ Same password rejection (must be different)
- ✅ New password authentication verification
- ✅ Old password invalidation confirmation

**Session Management:**
- ✅ Active session tracking for multiple session types
- ✅ Session count and type validation
- ✅ Single session per type enforcement
- ✅ Session invalidation on new login of same type

**Token Validation:**
- ✅ Valid token validation with user profile return
- ✅ Invalid token handling and error responses

#### Test Infrastructure

**Database Integration:**
- Uses `@DirtiesContext` for test isolation
- Automatic cleanup of test data between tests
- Real PostgreSQL database via Testcontainers
- Proper foreign key relationship testing

**HTTP Testing:**
- Full HTTP request/response cycle testing
- Proper header handling (Authorization, Content-Type)
- Status code validation
- Response body structure verification

**Security Testing:**
- Authentication requirement enforcement
- Token expiration handling
- Session type-specific access control
- Proper error responses for security violations

### Running Integration Tests

```bash
# Run all integration tests
./mvnw test -Dtest=*IntegrationTest

# Run authentication controller tests specifically
./mvnw test -Dtest=AuthenticationControllerIntegrationTest

# Run with specific test method
./mvnw test -Dtest=AuthenticationControllerIntegrationTest#testLoginWithWebSession_ShouldReturnTokensAndUserProfile
```

### Test Data Management

The integration tests use a comprehensive setup and teardown process:
- Clean database state before each test
- Test user creation with proper encoding
- Automatic cleanup after each test method
- Proper handling of foreign key relationships

This ensures reliable, repeatable tests that accurately reflect production behavior.

## Rate Limiting

Currently, no rate limiting is implemented. Consider implementing rate limiting for production use:
- Login attempts: 5 per minute per IP
- Token refresh: 10 per minute per user
- Admin operations: 100 per minute per admin user

## Versioning

The API currently does not use versioning. Future versions should consider:
- URL versioning: `/v1/auth/login`
- Header versioning: `Accept: application/vnd.gedsys.v1+json`
- Parameter versioning: `/auth/login?version=1`