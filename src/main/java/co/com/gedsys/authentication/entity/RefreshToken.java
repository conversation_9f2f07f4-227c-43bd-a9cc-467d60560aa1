package co.com.gedsys.authentication.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * RefreshToken entity for managing user session tokens with single-use policy.
 * Supports different session types (MOBILE, WEB) with single session policy.
 * Unique constraint is enforced at database level via partial index (uk_user_session_type_active).
 */
@Entity
@Table(name = "refresh_tokens",
       indexes = {
           @Index(name = "idx_refresh_tokens_user_id", columnList = "user_id"),
           @Index(name = "idx_refresh_tokens_expiry", columnList = "expiry_date"),
           @Index(name = "idx_refresh_tokens_used", columnList = "used")
       })
public class RefreshToken {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;
    
    @Column(name = "token", unique = true, nullable = false, length = 255)
    @NotBlank(message = "Token is required")
    private String token;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    @NotNull(message = "User is required")
    private User user;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "session_type", nullable = false, length = 20)
    @NotNull(message = "Session type is required")
    private SessionType sessionType;
    
    @Column(name = "expiry_date", nullable = false)
    @NotNull(message = "Expiry date is required")
    private LocalDateTime expiryDate;
    
    @Column(name = "created_at", nullable = false, updatable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;
    
    @Column(name = "used", nullable = false)
    private boolean used = false;

    @Column(name = "session_id", nullable = false, unique = true, length = 36)
    @NotBlank(message = "Session ID is required")
    private String sessionId;

    // Default constructor
    public RefreshToken() {}

    // Constructor with required fields
    public RefreshToken(String token, User user, SessionType sessionType, LocalDateTime expiryDate, String sessionId) {
        this.token = token;
        this.user = user;
        this.sessionType = sessionType;
        this.expiryDate = expiryDate;
        this.sessionId = sessionId;
    }

    // Legacy constructor for backward compatibility (will be deprecated)
    public RefreshToken(String token, User user, SessionType sessionType, LocalDateTime expiryDate) {
        this.token = token;
        this.user = user;
        this.sessionType = sessionType;
        this.expiryDate = expiryDate;
        this.sessionId = java.util.UUID.randomUUID().toString();
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getToken() {
        return token;
    }
    
    public void setToken(String token) {
        this.token = token;
    }
    
    public User getUser() {
        return user;
    }
    
    public void setUser(User user) {
        this.user = user;
    }
    
    public SessionType getSessionType() {
        return sessionType;
    }
    
    public void setSessionType(SessionType sessionType) {
        this.sessionType = sessionType;
    }
    
    public LocalDateTime getExpiryDate() {
        return expiryDate;
    }
    
    public void setExpiryDate(LocalDateTime expiryDate) {
        this.expiryDate = expiryDate;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public boolean isUsed() {
        return used;
    }
    
    public void setUsed(boolean used) {
        this.used = used;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    // Utility methods
    public boolean isExpired() {
        return LocalDateTime.now().isAfter(expiryDate);
    }
    
    public boolean isValid() {
        return !used && !isExpired();
    }
    
    public void markAsUsed() {
        this.used = true;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RefreshToken that = (RefreshToken) o;
        return Objects.equals(id, that.id) && Objects.equals(token, that.token);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id, token);
    }
    
    @Override
    public String toString() {
        return "RefreshToken{" +
                "id=" + id +
                ", token='" + token.substring(0, Math.min(token.length(), 10)) + "...'" +
                ", userId=" + (user != null ? user.getId() : null) +
                ", sessionType=" + sessionType +
                ", sessionId='" + (sessionId != null ? sessionId.substring(0, 8) + "..." : null) + "'" +
                ", expiryDate=" + expiryDate +
                ", createdAt=" + createdAt +
                ", used=" + used +
                '}';
    }
}