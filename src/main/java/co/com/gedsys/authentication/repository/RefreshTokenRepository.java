package co.com.gedsys.authentication.repository;

import co.com.gedsys.authentication.entity.RefreshToken;
import co.com.gedsys.authentication.entity.SessionType;
import co.com.gedsys.authentication.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for RefreshToken entity operations.
 * Provides methods for session management with single-use policy enforcement.
 */
@Repository
public interface RefreshTokenRepository extends JpaRepository<RefreshToken, Long> {
    
    /**
     * Find a refresh token by its token value.
     * 
     * @param token the token string to search for
     * @return Optional containing the refresh token if found, empty otherwise
     */
    Optional<RefreshToken> findByToken(String token);
    
    /**
     * Find a refresh token by user and session type.
     * This enforces the unique constraint of one token per user per session type.
     * 
     * @param user the user to search for
     * @param sessionType the session type to search for
     * @return Optional containing the refresh token if found, empty otherwise
     */
    Optional<RefreshToken> findByUserAndSessionType(User user, SessionType sessionType);
    
    /**
     * Find all refresh tokens for a specific user.
     * 
     * @param user the user to search for
     * @return List of refresh tokens for the user
     */
    List<RefreshToken> findByUser(User user);
    
    /**
     * Find all refresh tokens for a user with a specific session type.
     * 
     * @param user the user to search for
     * @param sessionType the session type to filter by
     * @return List of refresh tokens matching the criteria
     */
    List<RefreshToken> findAllByUserAndSessionType(User user, SessionType sessionType);
    
    /**
     * Find all valid (unused and not expired) refresh tokens for a user.
     * 
     * @param user the user to search for
     * @param currentTime the current time to check expiration against
     * @return List of valid refresh tokens for the user
     */
    @Query("SELECT rt FROM RefreshToken rt WHERE rt.user = :user AND rt.used = false AND rt.expiryDate > :currentTime")
    List<RefreshToken> findValidTokensByUser(@Param("user") User user, @Param("currentTime") LocalDateTime currentTime);
    
    /**
     * Find a valid (unused and not expired) refresh token by token string.
     * 
     * @param token the token string to search for
     * @param currentTime the current time to check expiration against
     * @return Optional containing the valid refresh token if found, empty otherwise
     */
    @Query("SELECT rt FROM RefreshToken rt WHERE rt.token = :token AND rt.used = false AND rt.expiryDate > :currentTime")
    Optional<RefreshToken> findValidTokenByToken(@Param("token") String token, @Param("currentTime") LocalDateTime currentTime);
    
    /**
     * Find all expired refresh tokens.
     * 
     * @param currentTime the current time to check expiration against
     * @return List of expired refresh tokens
     */
    @Query("SELECT rt FROM RefreshToken rt WHERE rt.expiryDate <= :currentTime")
    List<RefreshToken> findExpiredTokens(@Param("currentTime") LocalDateTime currentTime);
    
    /**
     * Find all used refresh tokens.
     * 
     * @return List of used refresh tokens
     */
    List<RefreshToken> findByUsedTrue();
    
    /**
     * Delete all refresh tokens for a specific user.
     * Used during user deletion or when invalidating all user sessions.
     * 
     * @param user the user whose tokens should be deleted
     */
    void deleteByUser(User user);
    
    /**
     * Delete refresh token by user and session type.
     * Used when invalidating a specific session type for a user.
     * 
     * @param user the user whose token should be deleted
     * @param sessionType the session type to delete
     */
    void deleteByUserAndSessionType(User user, SessionType sessionType);
    
    /**
     * Delete all expired refresh tokens.
     * This is used for cleanup operations to remove old tokens.
     * 
     * @param currentTime the current time to check expiration against
     * @return the number of deleted tokens
     */
    @Modifying
    @Query("DELETE FROM RefreshToken rt WHERE rt.expiryDate <= :currentTime")
    int deleteExpiredTokens(@Param("currentTime") LocalDateTime currentTime);
    
    /**
     * Delete all used refresh tokens.
     * This is used for cleanup operations to remove consumed tokens.
     * 
     * @return the number of deleted tokens
     */
    @Modifying
    @Query("DELETE FROM RefreshToken rt WHERE rt.used = true")
    int deleteUsedTokens();
    
    /**
     * Mark a refresh token as used by its token string.
     * This enforces the single-use policy.
     * 
     * @param token the token string to mark as used
     * @return the number of updated records (should be 1 if successful)
     */
    @Modifying
    @Query("UPDATE RefreshToken rt SET rt.used = true WHERE rt.token = :token")
    int markTokenAsUsed(@Param("token") String token);
    
    /**
     * Mark all refresh tokens for a user and session type as used.
     * Used when invalidating sessions of a specific type.
     * 
     * @param user the user whose tokens should be marked as used
     * @param sessionType the session type to mark as used
     * @return the number of updated records
     */
    @Modifying
    @Query("UPDATE RefreshToken rt SET rt.used = true WHERE rt.user = :user AND rt.sessionType = :sessionType")
    int markTokensAsUsedByUserAndSessionType(@Param("user") User user, @Param("sessionType") SessionType sessionType);
    
    /**
     * Count valid (unused and not expired) tokens for a user.
     * 
     * @param user the user to count tokens for
     * @param currentTime the current time to check expiration against
     * @return the number of valid tokens for the user
     */
    @Query("SELECT COUNT(rt) FROM RefreshToken rt WHERE rt.user = :user AND rt.used = false AND rt.expiryDate > :currentTime")
    long countValidTokensByUser(@Param("user") User user, @Param("currentTime") LocalDateTime currentTime);
    
    /**
     * Check if a user has a valid token for a specific session type.
     * 
     * @param user the user to check
     * @param sessionType the session type to check
     * @param currentTime the current time to check expiration against
     * @return true if the user has a valid token for the session type, false otherwise
     */
    @Query("SELECT COUNT(rt) > 0 FROM RefreshToken rt WHERE rt.user = :user AND rt.sessionType = :sessionType AND rt.used = false AND rt.expiryDate > :currentTime")
    boolean hasValidTokenForSessionType(@Param("user") User user, @Param("sessionType") SessionType sessionType, @Param("currentTime") LocalDateTime currentTime);
    
    /**
     * Count all valid (unused and not expired) tokens.
     * Used for metrics and monitoring.
     * 
     * @param currentTime the current time to check expiration against
     * @return the number of valid tokens in the system
     */
    @Query("SELECT COUNT(rt) FROM RefreshToken rt WHERE rt.used = false AND rt.expiryDate > :currentTime")
    long countValidTokens(@Param("currentTime") LocalDateTime currentTime);
    
    /**
     * Count all expired tokens.
     * Used for metrics and monitoring.
     *
     * @param currentTime the current time to check expiration against
     * @return the number of expired tokens in the system
     */
    @Query("SELECT COUNT(rt) FROM RefreshToken rt WHERE rt.expiryDate <= :currentTime")
    long countExpiredTokens(@Param("currentTime") LocalDateTime currentTime);

    /**
     * Check if an active session exists for the given user, session type, and session ID.
     * Used for access token validation to ensure the session is still active.
     *
     * @param user the user to check
     * @param sessionType the session type to check
     * @param sessionId the session ID to validate
     * @param currentTime the current time to check expiration against
     * @return true if an active session exists, false otherwise
     */
    boolean existsByUserAndSessionTypeAndSessionIdAndUsedFalseAndExpiryDateAfter(
        User user, SessionType sessionType, String sessionId, LocalDateTime currentTime);

    /**
     * Find an active refresh token by user, session type, and session ID.
     * Used for session validation and management.
     *
     * @param user the user to search for
     * @param sessionType the session type to search for
     * @param sessionId the session ID to search for
     * @param currentTime the current time to check expiration against
     * @return Optional containing the active refresh token if found, empty otherwise
     */
    @Query("SELECT rt FROM RefreshToken rt WHERE rt.user = :user AND rt.sessionType = :sessionType AND rt.sessionId = :sessionId AND rt.used = false AND rt.expiryDate > :currentTime")
    Optional<RefreshToken> findActiveTokenByUserAndSessionTypeAndSessionId(
        @Param("user") User user,
        @Param("sessionType") SessionType sessionType,
        @Param("sessionId") String sessionId,
        @Param("currentTime") LocalDateTime currentTime);
}