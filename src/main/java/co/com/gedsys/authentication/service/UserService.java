package co.com.gedsys.authentication.service;

import co.com.gedsys.authentication.dto.PagedResponse;
import co.com.gedsys.authentication.dto.UserProfileResponse;
import co.com.gedsys.authentication.entity.Role;
import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.entity.UserStatus;
import co.com.gedsys.authentication.exception.EmailAlreadyExistsException;
import co.com.gedsys.authentication.exception.UserNotFoundException;
import co.com.gedsys.authentication.exception.UsernameAlreadyExistsException;
import co.com.gedsys.authentication.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Service class for user management operations.
 * Provides CRUD operations, password encoding, and user validation.
 */
@Service
@Transactional
public class UserService {
    
    private static final Logger logger = LoggerFactory.getLogger(UserService.class);
    
    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final UsernameGenerator usernameGenerator;
    private final RefreshTokenService refreshTokenService;

    public UserService(UserRepository userRepository, PasswordEncoder passwordEncoder,
                      UsernameGenerator usernameGenerator, RefreshTokenService refreshTokenService) {
        this.userRepository = userRepository;
        this.passwordEncoder = passwordEncoder;
        this.usernameGenerator = usernameGenerator;
        this.refreshTokenService = refreshTokenService;
    }
    
    /**
     * Create a new user with encrypted password and auto-generated username.
     * Only administrators can create new users.
     *
     * @param user the user to create
     * @return the created user
     * @throws EmailAlreadyExistsException if email already exists (including deleted users)
     * @throws UsernameAlreadyExistsException if provided username already exists (including deleted users)
     */
    @PreAuthorize("hasRole('ADMIN')")
    public User createUser(User user) {
        logger.debug("Creating new user with email: {}", user.getEmail());

        // Check if email already exists (including deleted users)
        if (userRepository.existsByEmailIncludingDeleted(user.getEmail())) {
            logger.warn("Attempt to create user with existing email (including deleted): {}", user.getEmail());
            throw new EmailAlreadyExistsException(user.getEmail());
        }

        // Generate unique username if not provided
        if (user.getUsername() == null || user.getUsername().trim().isEmpty()) {
            String generatedUsername = usernameGenerator.generateUsername(
                user.getFirstName(),
                user.getLastName(),
                userRepository
            );
            user.setUsername(generatedUsername);
            logger.debug("Generated username: {} for user: {}", generatedUsername, user.getEmail());
        } else {
            // If username is provided, validate it's unique (including deleted users)
            if (userRepository.existsByUsernameIncludingDeleted(user.getUsername())) {
                logger.warn("Attempt to create user with existing username (including deleted): {}", user.getUsername());
                throw new UsernameAlreadyExistsException(user.getUsername());
            }
        }

        // Encode password
        user.setPassword(passwordEncoder.encode(user.getPassword()));

        // Set default role if not specified
        if (user.getRole() == null) {
            user.setRole(Role.USER);
        }

        // Set default status if not specified
        if (user.getStatus() == null) {
            user.setStatus(UserStatus.ACTIVE);
        }

        User savedUser = userRepository.save(user);
        logger.info("User created successfully with ID: {}, email: {}, and username: {}",
                   savedUser.getId(), savedUser.getEmail(), savedUser.getUsername());

        return savedUser;
    }
    
    /**
     * Update an existing user.
     * Only administrators can update user information.
     * 
     * @param userId the ID of the user to update
     * @param updatedUser the updated user data
     * @return the updated user
     * @throws UserNotFoundException if user not found
     * @throws EmailAlreadyExistsException if email already exists for another user
     */
    @PreAuthorize("hasRole('ADMIN')")
    public User updateUser(Long userId, User updatedUser) {
        logger.debug("Updating user with ID: {}", userId);
        
        User existingUser = userRepository.findById(userId)
                .orElseThrow(() -> new UserNotFoundException(userId));
        
        // Check if email is being changed and if it already exists (including deleted users)
        if (!existingUser.getEmail().equals(updatedUser.getEmail()) &&
            userRepository.existsByEmailIncludingDeleted(updatedUser.getEmail())) {
            logger.warn("Attempt to update user {} with existing email (including deleted): {}", userId, updatedUser.getEmail());
            throw new EmailAlreadyExistsException(updatedUser.getEmail());
        }

        // Check if username is being changed and if it already exists (including deleted users)
        if (updatedUser.getUsername() != null &&
            !updatedUser.getUsername().equals(existingUser.getUsername()) &&
            userRepository.existsByUsernameIncludingDeleted(updatedUser.getUsername())) {
            logger.warn("Attempt to update user {} with existing username (including deleted): {}", userId, updatedUser.getUsername());
            throw new UsernameAlreadyExistsException(updatedUser.getUsername());
        }

        // Update fields
        existingUser.setEmail(updatedUser.getEmail());
        existingUser.setFirstName(updatedUser.getFirstName());
        existingUser.setLastName(updatedUser.getLastName());
        existingUser.setRole(updatedUser.getRole());
        existingUser.setEnabled(updatedUser.isEnabled());

        // Update username if provided
        if (updatedUser.getUsername() != null && !updatedUser.getUsername().trim().isEmpty()) {
            existingUser.setUsername(updatedUser.getUsername());
        }

        // Update status if provided
        if (updatedUser.getStatus() != null) {
            existingUser.setStatus(updatedUser.getStatus());
        }
        
        // Only update password if provided
        if (updatedUser.getPassword() != null && !updatedUser.getPassword().isEmpty()) {
            existingUser.setPassword(passwordEncoder.encode(updatedUser.getPassword()));
        }
        
        User savedUser = userRepository.save(existingUser);
        logger.info("User updated successfully with ID: {}", savedUser.getId());
        
        return savedUser;
    }
    
    /**
     * Delete a user by ID (hard delete).
     * Only administrators can delete users.
     *
     * @param userId the ID of the user to delete
     * @throws UserNotFoundException if user not found
     */
    @PreAuthorize("hasRole('ADMIN')")
    public void deleteUser(Long userId) {
        logger.debug("Hard deleting user with ID: {}", userId);

        if (!userRepository.existsById(userId)) {
            throw new UserNotFoundException(userId);
        }

        userRepository.deleteById(userId);
        logger.info("User hard deleted successfully with ID: {}", userId);
    }

    /**
     * Soft delete a user by setting status to DELETED.
     * Only administrators can delete users.
     * This method preserves the user data but marks them as deleted and invalidates all active sessions.
     *
     * @param userId the ID of the user to soft delete
     * @throws UserNotFoundException if user not found
     * @throws IllegalStateException if user is already deleted
     */
    @PreAuthorize("hasRole('ADMIN')")
    public void softDeleteUser(Long userId) {
        logger.debug("Soft deleting user with ID: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new UserNotFoundException(userId));

        // Check if user is already deleted
        if (user.getStatus() == UserStatus.DELETED) {
            logger.warn("Attempt to soft delete already deleted user with ID: {}", userId);
            throw new IllegalStateException("User is already deleted");
        }

        // Get session count before invalidation for audit logging
        long activeSessionCount = refreshTokenService.getValidTokenCountForUser(user);

        // Set status to DELETED and disable the user
        user.setStatus(UserStatus.DELETED);
        user.setEnabled(false);

        // Save user changes first
        userRepository.save(user);

        // Invalidate all active sessions and tokens
        refreshTokenService.invalidateAllUserTokens(user);

        // Log audit event for session invalidation
        logger.info("SECURITY_EVENT: User soft deletion completed - user: {} (email: {}, username: {}) marked as deleted and {} active sessions invalidated",
                   userId, user.getEmail(), user.getUsername(), activeSessionCount);
    }
    
    /**
     * Find a user by ID.
     * 
     * @param userId the user ID
     * @return the user
     * @throws UserNotFoundException if user not found
     */
    @Transactional(readOnly = true)
    public User findById(Long userId) {
        logger.debug("Finding user by ID: {}", userId);
        
        return userRepository.findById(userId)
                .orElseThrow(() -> new UserNotFoundException(userId));
    }
    
    /**
     * Find a user by email.
     * 
     * @param email the email address
     * @return Optional containing the user if found
     */
    @Transactional(readOnly = true)
    public Optional<User> findByEmail(String email) {
        logger.debug("Finding user by email: {}", email);
        
        return userRepository.findByEmail(email);
    }
    
    /**
     * Get user profile response by ID.
     * 
     * @param userId the user ID
     * @return the user profile response
     * @throws UserNotFoundException if user not found
     */
    @Transactional(readOnly = true)
    public UserProfileResponse getUserProfile(Long userId) {
        logger.debug("Getting user profile for ID: {}", userId);
        
        User user = findById(userId);
        return new UserProfileResponse(user);
    }
    
    /**
     * Get paginated list of users with optional filters.
     * Only administrators can access user listings.
     * 
     * @param email email filter (partial match)
     * @param enabled enabled status filter
     * @param role role filter
     * @param pageable pagination parameters
     * @return paginated response of user profiles
     */
    @PreAuthorize("hasRole('ADMIN')")
    @Transactional(readOnly = true)
    public PagedResponse<UserProfileResponse> getUsers(String email, Boolean enabled, Role role, Pageable pageable) {
        logger.debug("Getting users with filters - email: {}, enabled: {}, role: {}, page: {}", 
                    email, enabled, role, pageable.getPageNumber());
        
        Page<User> userPage = userRepository.findUsersWithFilters(email, enabled, role, pageable);
        
        Page<UserProfileResponse> responsePage = userPage.map(UserProfileResponse::new);
        
        return PagedResponse.of(responsePage);
    }
    
    /**
     * Get all users with pagination.
     * Only administrators can access user listings.
     * 
     * @param pageable pagination parameters
     * @return paginated response of user profiles
     */
    @PreAuthorize("hasRole('ADMIN')")
    @Transactional(readOnly = true)
    public PagedResponse<UserProfileResponse> getAllUsers(Pageable pageable) {
        logger.debug("Getting all users with pagination - page: {}", pageable.getPageNumber());
        
        Page<User> userPage = userRepository.findAll(pageable);
        Page<UserProfileResponse> responsePage = userPage.map(UserProfileResponse::new);
        
        return PagedResponse.of(responsePage);
    }
    
    /**
     * Validate user credentials with flexible login (username or email).
     *
     * @param identifier the username or email address
     * @param rawPassword the raw password
     * @return the user if credentials are valid
     * @throws UserNotFoundException if user not found or credentials invalid
     */
    @Transactional(readOnly = true)
    public User validateUserCredentials(String identifier, String rawPassword) {
        logger.debug("SECURITY_EVENT: Credential validation - validating credentials for identifier: {}", identifier);

        User user = userRepository.findByUsernameOrEmail(identifier)
                .orElseThrow(() -> {
                    logger.warn("SECURITY_EVENT: Authentication failed - user not found for identifier: {}", identifier);
                    return new UserNotFoundException("identifier", identifier);
                });

        // Check if user is deleted
        if (user.getStatus() == UserStatus.DELETED) {
            logger.warn("SECURITY_EVENT: Authentication failed - login attempt for deleted user: {} (ID: {})",
                       identifier, user.getId());
            throw new UserNotFoundException("User account has been deleted");
        }

        // Check if user is enabled (legacy check)
        if (!user.isEnabled()) {
            logger.warn("SECURITY_EVENT: Authentication failed - login attempt for disabled user: {} (ID: {})",
                       identifier, user.getId());
            throw new UserNotFoundException("User account is disabled");
        }

        // Check if user is active (new status check)
        if (!user.isActive()) {
            logger.warn("SECURITY_EVENT: Authentication failed - login attempt for inactive user: {} (ID: {})",
                       identifier, user.getId());
            throw new UserNotFoundException("User account is inactive");
        }

        if (!passwordEncoder.matches(rawPassword, user.getPassword())) {
            logger.warn("SECURITY_EVENT: Authentication failed - invalid password attempt for user: {} (ID: {})",
                       identifier, user.getId());
            throw new UserNotFoundException("Invalid credentials");
        }

        logger.debug("SECURITY_EVENT: Credential validation successful - credentials validated for user: {} (ID: {})",
                    identifier, user.getId());
        return user;
    }
    
    /**
     * Change user password.
     * 
     * @param userId the user ID
     * @param currentPassword the current password
     * @param newPassword the new password
     * @throws UserNotFoundException if user not found or current password is invalid
     */
    public void changePassword(Long userId, String currentPassword, String newPassword) {
        logger.debug("SECURITY_EVENT: Password change attempt - user: {} requesting password change", userId);
        
        User user = findById(userId);
        
        // Validate current password
        if (!passwordEncoder.matches(currentPassword, user.getPassword())) {
            logger.warn("SECURITY_EVENT: Password change failed - invalid current password for user: {} (email: {})", 
                       userId, user.getEmail());
            throw new UserNotFoundException("Current password is incorrect");
        }
        
        // Validate new password is different
        if (passwordEncoder.matches(newPassword, user.getPassword())) {
            logger.warn("SECURITY_EVENT: Password change failed - new password same as current for user: {} (email: {})", 
                       userId, user.getEmail());
            throw new IllegalArgumentException("New password must be different from current password");
        }
        
        // Update password
        user.setPassword(passwordEncoder.encode(newPassword));
        userRepository.save(user);
        
        logger.info("SECURITY_EVENT: Password changed - password successfully changed for user: {} (email: {})", 
                   userId, user.getEmail());
    }
    
    /**
     * Check if email exists.
     * 
     * @param email the email to check
     * @return true if email exists, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean emailExists(String email) {
        return userRepository.existsByEmail(email);
    }
    
    /**
     * Get user count by enabled status.
     * Only administrators can access user statistics.
     * 
     * @param enabled the enabled status
     * @return count of users
     */
    @PreAuthorize("hasRole('ADMIN')")
    @Transactional(readOnly = true)
    public long getUserCountByEnabled(boolean enabled) {
        return userRepository.countByEnabled(enabled);
    }
    
    /**
     * Get user count by role.
     * Only administrators can access user statistics.
     * 
     * @param role the role
     * @return count of users
     */
    @PreAuthorize("hasRole('ADMIN')")
    @Transactional(readOnly = true)
    public long getUserCountByRole(Role role) {
        return userRepository.countByRole(role);
    }
}