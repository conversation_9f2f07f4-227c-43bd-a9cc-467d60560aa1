package co.com.gedsys.authentication.dto;

import co.com.gedsys.authentication.entity.Role;
import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.entity.UserStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Unit tests for AdminUserResponse DTO.
 */
@DisplayName("AdminUserResponse Tests")
class AdminUserResponseTest {

    @Test
    @DisplayName("Should create AdminUserResponse from User entity with username")
    void shouldCreateAdminUserResponseFromUserEntityWithUsername() {
        // Given
        User user = new User();
        user.setId(1L);
        user.setEmail("<EMAIL>");
        user.setUsername("adminuser");
        user.setFirstName("Admin");
        user.setLastName("User");
        user.setRole(Role.ADMIN);
        user.setStatus(UserStatus.ACTIVE);
        user.setEnabled(true);
        LocalDateTime now = LocalDateTime.now();
        user.setCreatedAt(now);
        user.setUpdatedAt(now);

        // When
        AdminUserResponse response = new AdminUserResponse(user);

        // Then
        assertThat(response.getId()).isEqualTo(1L);
        assertThat(response.getEmail()).isEqualTo("<EMAIL>");
        assertThat(response.getUsername()).isEqualTo("adminuser");
        assertThat(response.getFirstName()).isEqualTo("Admin");
        assertThat(response.getLastName()).isEqualTo("User");
        assertThat(response.getRole()).isEqualTo(Role.ADMIN);
        assertThat(response.isEnabled()).isTrue();
        assertThat(response.getCreatedAt()).isEqualTo(now);
        assertThat(response.getUpdatedAt()).isEqualTo(now);
    }

    @Test
    @DisplayName("Should handle null username in User entity")
    void shouldHandleNullUsernameInUserEntity() {
        // Given
        User user = new User();
        user.setId(2L);
        user.setEmail("<EMAIL>");
        user.setUsername(null); // Username can be null
        user.setFirstName("Regular");
        user.setLastName("User");
        user.setRole(Role.USER);
        user.setEnabled(false);
        LocalDateTime now = LocalDateTime.now();
        user.setCreatedAt(now);
        user.setUpdatedAt(now);

        // When
        AdminUserResponse response = new AdminUserResponse(user);

        // Then
        assertThat(response.getId()).isEqualTo(2L);
        assertThat(response.getEmail()).isEqualTo("<EMAIL>");
        assertThat(response.getUsername()).isNull();
        assertThat(response.getFirstName()).isEqualTo("Regular");
        assertThat(response.getLastName()).isEqualTo("User");
        assertThat(response.getRole()).isEqualTo(Role.USER);
        assertThat(response.isEnabled()).isFalse();
    }

    @Test
    @DisplayName("Should set and get username correctly")
    void shouldSetAndGetUsernameCorrectly() {
        // Given
        AdminUserResponse response = new AdminUserResponse();

        // When
        response.setUsername("newadminuser");

        // Then
        assertThat(response.getUsername()).isEqualTo("newadminuser");
    }

    @Test
    @DisplayName("Should create empty AdminUserResponse with default constructor")
    void shouldCreateEmptyAdminUserResponseWithDefaultConstructor() {
        // When
        AdminUserResponse response = new AdminUserResponse();

        // Then
        assertThat(response.getId()).isNull();
        assertThat(response.getEmail()).isNull();
        assertThat(response.getUsername()).isNull();
        assertThat(response.getFirstName()).isNull();
        assertThat(response.getLastName()).isNull();
        assertThat(response.getRole()).isNull();
        assertThat(response.getCreatedAt()).isNull();
        assertThat(response.getUpdatedAt()).isNull();
        assertThat(response.isEnabled()).isFalse(); // Default boolean value
    }

    @Test
    @DisplayName("Should handle all fields correctly")
    void shouldHandleAllFieldsCorrectly() {
        // Given
        AdminUserResponse response = new AdminUserResponse();
        LocalDateTime createdAt = LocalDateTime.now().minusDays(1);
        LocalDateTime updatedAt = LocalDateTime.now();

        // When
        response.setId(100L);
        response.setEmail("<EMAIL>");
        response.setUsername("completeadmin");
        response.setFirstName("Complete");
        response.setLastName("Admin");
        response.setRole(Role.ADMIN);
        response.setEnabled(true);
        response.setCreatedAt(createdAt);
        response.setUpdatedAt(updatedAt);

        // Then
        assertThat(response.getId()).isEqualTo(100L);
        assertThat(response.getEmail()).isEqualTo("<EMAIL>");
        assertThat(response.getUsername()).isEqualTo("completeadmin");
        assertThat(response.getFirstName()).isEqualTo("Complete");
        assertThat(response.getLastName()).isEqualTo("Admin");
        assertThat(response.getRole()).isEqualTo(Role.ADMIN);
        assertThat(response.isEnabled()).isTrue();
        assertThat(response.getCreatedAt()).isEqualTo(createdAt);
        assertThat(response.getUpdatedAt()).isEqualTo(updatedAt);
    }
}
