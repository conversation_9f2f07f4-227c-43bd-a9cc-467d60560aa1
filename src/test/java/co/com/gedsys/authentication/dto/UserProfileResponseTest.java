package co.com.gedsys.authentication.dto;

import co.com.gedsys.authentication.entity.Role;
import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.entity.UserStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Unit tests for UserProfileResponse DTO.
 */
@DisplayName("UserProfileResponse Tests")
class UserProfileResponseTest {

    @Test
    @DisplayName("Should create UserProfileResponse from User entity with username")
    void shouldCreateUserProfileResponseFromUserEntityWithUsername() {
        // Given
        User user = new User();
        user.setId(1L);
        user.setEmail("<EMAIL>");
        user.setUsername("testuser");
        user.setFirstName("John");
        user.setLastName("Doe");
        user.setRole(Role.USER);
        user.setStatus(UserStatus.ACTIVE);
        user.setCreatedAt(LocalDateTime.now());

        // When
        UserProfileResponse response = new UserProfileResponse(user);

        // Then
        assertThat(response.getId()).isEqualTo(1L);
        assertThat(response.getEmail()).isEqualTo("<EMAIL>");
        assertThat(response.getUsername()).isEqualTo("testuser");
        assertThat(response.getFirstName()).isEqualTo("John");
        assertThat(response.getLastName()).isEqualTo("Doe");
        assertThat(response.getRole()).isEqualTo(Role.USER);
        assertThat(response.getCreatedAt()).isNotNull();
    }

    @Test
    @DisplayName("Should handle null username in User entity")
    void shouldHandleNullUsernameInUserEntity() {
        // Given
        User user = new User();
        user.setId(2L);
        user.setEmail("<EMAIL>");
        user.setUsername(null); // Username can be null
        user.setFirstName("Jane");
        user.setLastName("Smith");
        user.setRole(Role.ADMIN);
        user.setCreatedAt(LocalDateTime.now());

        // When
        UserProfileResponse response = new UserProfileResponse(user);

        // Then
        assertThat(response.getId()).isEqualTo(2L);
        assertThat(response.getEmail()).isEqualTo("<EMAIL>");
        assertThat(response.getUsername()).isNull();
        assertThat(response.getFirstName()).isEqualTo("Jane");
        assertThat(response.getLastName()).isEqualTo("Smith");
        assertThat(response.getRole()).isEqualTo(Role.ADMIN);
    }

    @Test
    @DisplayName("Should set and get username correctly")
    void shouldSetAndGetUsernameCorrectly() {
        // Given
        UserProfileResponse response = new UserProfileResponse();

        // When
        response.setUsername("newusername");

        // Then
        assertThat(response.getUsername()).isEqualTo("newusername");
    }

    @Test
    @DisplayName("Should create empty UserProfileResponse with default constructor")
    void shouldCreateEmptyUserProfileResponseWithDefaultConstructor() {
        // When
        UserProfileResponse response = new UserProfileResponse();

        // Then
        assertThat(response.getId()).isNull();
        assertThat(response.getEmail()).isNull();
        assertThat(response.getUsername()).isNull();
        assertThat(response.getFirstName()).isNull();
        assertThat(response.getLastName()).isNull();
        assertThat(response.getRole()).isNull();
        assertThat(response.getCreatedAt()).isNull();
    }

    @Test
    @DisplayName("Should handle all fields correctly")
    void shouldHandleAllFieldsCorrectly() {
        // Given
        UserProfileResponse response = new UserProfileResponse();
        LocalDateTime now = LocalDateTime.now();

        // When
        response.setId(100L);
        response.setEmail("<EMAIL>");
        response.setUsername("completeuser");
        response.setFirstName("Complete");
        response.setLastName("User");
        response.setRole(Role.ADMIN);
        response.setCreatedAt(now);

        // Then
        assertThat(response.getId()).isEqualTo(100L);
        assertThat(response.getEmail()).isEqualTo("<EMAIL>");
        assertThat(response.getUsername()).isEqualTo("completeuser");
        assertThat(response.getFirstName()).isEqualTo("Complete");
        assertThat(response.getLastName()).isEqualTo("User");
        assertThat(response.getRole()).isEqualTo(Role.ADMIN);
        assertThat(response.getCreatedAt()).isEqualTo(now);
    }
}
