package co.com.gedsys.authentication.exception;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.*;

/**
 * Unit tests for custom exceptions.
 */
@DisplayName("Custom Exceptions Tests")
class CustomExceptionsTest {

    @Test
    @DisplayName("UsernameAlreadyExistsException should create with username")
    void usernameAlreadyExistsExceptionShouldCreateWithUsername() {
        // Given
        String username = "testuser";
        
        // When
        UsernameAlreadyExistsException exception = new UsernameAlreadyExistsException(username);
        
        // Then
        assertThat(exception.getMessage()).isEqualTo("User with username 'testuser' already exists");
        assertThat(exception).isInstanceOf(RuntimeException.class);
    }

    @Test
    @DisplayName("UsernameAlreadyExistsException should create with message and cause")
    void usernameAlreadyExistsExceptionShouldCreateWithMessageAndCause() {
        // Given
        String message = "Custom message";
        Throwable cause = new RuntimeException("Root cause");
        
        // When
        UsernameAlreadyExistsException exception = new UsernameAlreadyExistsException(message, cause);
        
        // Then
        assertThat(exception.getMessage()).isEqualTo("Custom message");
        assertThat(exception.getCause()).isEqualTo(cause);
    }

    @Test
    @DisplayName("UserDeletedException should create with message")
    void userDeletedExceptionShouldCreateWithMessage() {
        // Given
        String message = "User has been deleted";
        
        // When
        UserDeletedException exception = new UserDeletedException(message);
        
        // Then
        assertThat(exception.getMessage()).isEqualTo("User has been deleted");
        assertThat(exception).isInstanceOf(RuntimeException.class);
    }

    @Test
    @DisplayName("UserDeletedException should create with message and cause")
    void userDeletedExceptionShouldCreateWithMessageAndCause() {
        // Given
        String message = "Custom message";
        Throwable cause = new RuntimeException("Root cause");
        
        // When
        UserDeletedException exception = new UserDeletedException(message, cause);
        
        // Then
        assertThat(exception.getMessage()).isEqualTo("Custom message");
        assertThat(exception.getCause()).isEqualTo(cause);
    }

    @Test
    @DisplayName("UserDeletedException should create with userId")
    void userDeletedExceptionShouldCreateWithUserId() {
        // Given
        Long userId = 123L;
        
        // When
        UserDeletedException exception = new UserDeletedException(userId);
        
        // Then
        assertThat(exception.getMessage()).isEqualTo("User with ID 123 has been deleted");
    }

    @Test
    @DisplayName("UserDeletedException should create with identifier and type")
    void userDeletedExceptionShouldCreateWithIdentifierAndType() {
        // Given
        String identifier = "<EMAIL>";
        String identifierType = "email";
        
        // When
        UserDeletedException exception = new UserDeletedException(identifier, identifierType);
        
        // Then
        assertThat(exception.getMessage()).isEqualTo("User with email '<EMAIL>' has been deleted");
    }
}
