package co.com.gedsys.authentication.exception;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import jakarta.servlet.http.HttpServletRequest;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for GlobalExceptionHandler custom exceptions handling.
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("GlobalExceptionHandler Custom Exceptions Tests")
class GlobalExceptionHandlerCustomExceptionsTest {

    @Mock
    private HttpServletRequest request;

    private GlobalExceptionHandler exceptionHandler;

    @BeforeEach
    void setUp() {
        exceptionHandler = new GlobalExceptionHandler();
    }

    @Test
    @DisplayName("Should handle UsernameAlreadyExistsException")
    void shouldHandleUsernameAlreadyExistsException() {
        // Given
        when(request.getRequestURI()).thenReturn("/api/test");
        when(request.getRemoteAddr()).thenReturn("127.0.0.1");
        UsernameAlreadyExistsException exception = new UsernameAlreadyExistsException("testuser");

        // When
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleUsernameAlreadyExists(exception, request);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.CONFLICT);
        assertThat(response.getBody()).isNotNull();
        ErrorResponse body = response.getBody();
        assertThat(body.getError()).isEqualTo("USERNAME_ALREADY_EXISTS");
        assertThat(body.getMessage()).isEqualTo("User with username 'testuser' already exists");
        assertThat(body.getStatus()).isEqualTo(409);
        assertThat(body.getPath()).isEqualTo("/api/test");
        assertThat(body.getTimestamp()).isNotNull();
    }

    @Test
    @DisplayName("Should handle UserDeletedException as UserNotFoundException for security")
    void shouldHandleUserDeletedExceptionAsUserNotFoundForSecurity() {
        // Given
        when(request.getRequestURI()).thenReturn("/api/test");
        when(request.getRemoteAddr()).thenReturn("127.0.0.1");
        UserDeletedException exception = new UserDeletedException("User has been deleted");

        // When
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleUserDeleted(exception, request);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.NOT_FOUND);
        assertThat(response.getBody()).isNotNull();
        ErrorResponse body = response.getBody();
        assertThat(body.getError()).isEqualTo("USER_NOT_FOUND");
        assertThat(body.getMessage()).isEqualTo("User not found");
        assertThat(body.getStatus()).isEqualTo(404);
        assertThat(body.getPath()).isEqualTo("/api/test");
        assertThat(body.getTimestamp()).isNotNull();
    }

    @Test
    @DisplayName("Should handle UsernameAlreadyExistsException with X-Forwarded-For header")
    void shouldHandleUsernameAlreadyExistsExceptionWithXForwardedForHeader() {
        // Given
        when(request.getRequestURI()).thenReturn("/api/test");
        when(request.getHeader("X-Forwarded-For")).thenReturn("192.168.1.100");
        UsernameAlreadyExistsException exception = new UsernameAlreadyExistsException("duplicateuser");

        // When
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleUsernameAlreadyExists(exception, request);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.CONFLICT);
        assertThat(response.getBody()).isNotNull();
        ErrorResponse body = response.getBody();
        assertThat(body.getError()).isEqualTo("USERNAME_ALREADY_EXISTS");
        assertThat(body.getMessage()).isEqualTo("User with username 'duplicateuser' already exists");
    }

    @Test
    @DisplayName("Should handle UserDeletedException with different constructor")
    void shouldHandleUserDeletedExceptionWithDifferentConstructor() {
        // Given
        when(request.getRequestURI()).thenReturn("/api/test");
        when(request.getRemoteAddr()).thenReturn("127.0.0.1");
        UserDeletedException exception = new UserDeletedException(123L);

        // When
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleUserDeleted(exception, request);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.NOT_FOUND);
        assertThat(response.getBody()).isNotNull();
        ErrorResponse body = response.getBody();
        assertThat(body.getError()).isEqualTo("USER_NOT_FOUND");
        // Message should be generic for security, not revealing the actual exception message
        assertThat(body.getMessage()).isEqualTo("User not found");
    }
}
