package co.com.gedsys.authentication.integration;

import co.com.gedsys.authentication.dto.AuthResponse;
import co.com.gedsys.authentication.dto.CreateUserRequest;
import co.com.gedsys.authentication.dto.UserProfileResponse;
import co.com.gedsys.authentication.entity.Role;
import co.com.gedsys.authentication.entity.SessionType;
import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.entity.UserStatus;
import co.com.gedsys.authentication.repository.UserRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests to verify backward compatibility with existing endpoints and functionality.
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
@DisplayName("Backward Compatibility Integration Tests")
class BackwardCompatibilityIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    private User testUser;
    private final String testPassword = "password123";
    private final String testEmail = "<EMAIL>";

    @BeforeEach
    void setUp() {
        userRepository.deleteAll();
        
        // Create test user with new schema
        testUser = new User();
        testUser.setEmail(testEmail);
        testUser.setUsername("testuser");
        testUser.setPassword(passwordEncoder.encode(testPassword));
        testUser.setFirstName("Test");
        testUser.setLastName("User");
        testUser.setRole(Role.USER);
        testUser.setStatus(UserStatus.ACTIVE);
        testUser.setEnabled(true);
        testUser = userRepository.save(testUser);
    }

    @Test
    @DisplayName("Should maintain compatibility with old login request format")
    void shouldMaintainCompatibilityWithOldLoginRequestFormat() throws Exception {
        // Given - Old format login request (using email field instead of identifier)
        String oldFormatJson = String.format(
                "{\"email\":\"%s\",\"password\":\"%s\",\"sessionType\":\"WEB\"}", 
                testEmail, testPassword);

        // When
        MvcResult result = mockMvc.perform(post("/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(oldFormatJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.accessToken").exists())
                .andExpect(jsonPath("$.refreshToken").exists())
                .andExpect(jsonPath("$.user.email").value(testEmail))
                .andExpect(jsonPath("$.user.username").value("testuser"))
                .andReturn();

        // Then
        AuthResponse response = objectMapper.readValue(
                result.getResponse().getContentAsString(), 
                AuthResponse.class);

        assertThat(response.getAccessToken()).isNotNull();
        assertThat(response.getRefreshToken()).isNotNull();
        assertThat(response.getUser().getEmail()).isEqualTo(testEmail);
        assertThat(response.getUser().getUsername()).isEqualTo("testuser");
    }

    @Test
    @DisplayName("Should maintain compatibility with profile endpoint response format")
    void shouldMaintainCompatibilityWithProfileEndpointResponseFormat() throws Exception {
        // Given - Login to get access token
        String loginJson = String.format(
                "{\"identifier\":\"%s\",\"password\":\"%s\",\"sessionType\":\"WEB\"}", 
                testEmail, testPassword);

        MvcResult loginResult = mockMvc.perform(post("/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(loginJson))
                .andExpect(status().isOk())
                .andReturn();

        AuthResponse authResponse = objectMapper.readValue(
                loginResult.getResponse().getContentAsString(), 
                AuthResponse.class);

        // When - Get profile
        MvcResult profileResult = mockMvc.perform(get("/auth/profile")
                .header("Authorization", "Bearer " + authResponse.getAccessToken()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.email").value(testEmail))
                .andExpect(jsonPath("$.username").value("testuser")) // New field
                .andExpect(jsonPath("$.firstName").value("Test"))
                .andExpect(jsonPath("$.lastName").value("User"))
                .andExpect(jsonPath("$.role").value("USER"))
                .andExpect(jsonPath("$.createdAt").exists())
                .andReturn();

        // Then - Response should include all expected fields including new username
        UserProfileResponse profile = objectMapper.readValue(
                profileResult.getResponse().getContentAsString(), 
                UserProfileResponse.class);

        assertThat(profile.getId()).isNotNull();
        assertThat(profile.getEmail()).isEqualTo(testEmail);
        assertThat(profile.getUsername()).isEqualTo("testuser");
        assertThat(profile.getFirstName()).isEqualTo("Test");
        assertThat(profile.getLastName()).isEqualTo("User");
        assertThat(profile.getRole()).isEqualTo(Role.USER);
        assertThat(profile.getCreatedAt()).isNotNull();
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("Should maintain compatibility with admin user creation without username")
    void shouldMaintainCompatibilityWithAdminUserCreationWithoutUsername() throws Exception {
        // Given - Old format create user request (without username field)
        CreateUserRequest request = new CreateUserRequest();
        request.setEmail("<EMAIL>");
        request.setPassword("password123");
        request.setFirstName("New");
        request.setLastName("User");
        request.setRole(Role.USER);
        request.setEnabled(true);
        // Note: username is not set, should be auto-generated

        // When
        MvcResult result = mockMvc.perform(post("/admin/users")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.username").exists()) // Should be auto-generated
                .andExpect(jsonPath("$.firstName").value("New"))
                .andExpect(jsonPath("$.lastName").value("User"))
                .andExpect(jsonPath("$.role").value("USER"))
                .andReturn();

        // Then
        UserProfileResponse response = objectMapper.readValue(
                result.getResponse().getContentAsString(), 
                UserProfileResponse.class);

        assertThat(response.getUsername()).isNotNull();
        assertThat(response.getUsername()).isEqualTo("nuser"); // Generated from "New User"

        // Verify in database
        Optional<User> savedUser = userRepository.findByEmail("<EMAIL>");
        assertThat(savedUser).isPresent();
        assertThat(savedUser.get().getUsername()).isEqualTo("nuser");
        assertThat(savedUser.get().getStatus()).isEqualTo(UserStatus.ACTIVE);
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("Should maintain compatibility with admin user list endpoint")
    void shouldMaintainCompatibilityWithAdminUserListEndpoint() throws Exception {
        // Given - Create additional user
        User additionalUser = new User();
        additionalUser.setEmail("<EMAIL>");
        additionalUser.setUsername("additional");
        additionalUser.setPassword(passwordEncoder.encode("password"));
        additionalUser.setFirstName("Additional");
        additionalUser.setLastName("User");
        additionalUser.setRole(Role.ADMIN);
        additionalUser.setStatus(UserStatus.ACTIVE);
        additionalUser.setEnabled(true);
        userRepository.save(additionalUser);

        // When
        MvcResult result = mockMvc.perform(get("/admin/users"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content[*].id").exists())
                .andExpect(jsonPath("$.content[*].email").exists())
                .andExpect(jsonPath("$.content[*].username").exists()) // New field
                .andExpect(jsonPath("$.content[*].firstName").exists())
                .andExpect(jsonPath("$.content[*].lastName").exists())
                .andExpect(jsonPath("$.content[*].role").exists())
                .andReturn();

        // Then - All users should include username field
        String responseContent = result.getResponse().getContentAsString();
        assertThat(responseContent).contains("\"username\":");
        assertThat(responseContent).contains("testuser");
        assertThat(responseContent).contains("additional");
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("Should maintain compatibility with admin user filters")
    void shouldMaintainCompatibilityWithAdminUserFilters() throws Exception {
        // Given - Create users with different roles
        User adminUser = new User();
        adminUser.setEmail("<EMAIL>");
        adminUser.setUsername("adminuser");
        adminUser.setPassword(passwordEncoder.encode("password"));
        adminUser.setFirstName("Admin");
        adminUser.setLastName("User");
        adminUser.setRole(Role.ADMIN);
        adminUser.setStatus(UserStatus.ACTIVE);
        adminUser.setEnabled(true);
        userRepository.save(adminUser);

        // When - Filter by role (existing functionality)
        mockMvc.perform(get("/admin/users")
                .param("role", "USER"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content[0].role").value("USER"))
                .andExpect(jsonPath("$.content[0].username").exists());

        // When - Filter by enabled status (existing functionality)
        mockMvc.perform(get("/admin/users")
                .param("enabled", "true"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content[*].username").exists());
    }

    @Test
    @DisplayName("Should maintain compatibility with refresh token functionality")
    void shouldMaintainCompatibilityWithRefreshTokenFunctionality() throws Exception {
        // Given - Login to get tokens
        String loginJson = String.format(
                "{\"identifier\":\"%s\",\"password\":\"%s\",\"sessionType\":\"WEB\"}", 
                testEmail, testPassword);

        MvcResult loginResult = mockMvc.perform(post("/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(loginJson))
                .andExpect(status().isOk())
                .andReturn();

        AuthResponse authResponse = objectMapper.readValue(
                loginResult.getResponse().getContentAsString(), 
                AuthResponse.class);

        // When - Refresh token
        String refreshJson = String.format(
                "{\"refreshToken\":\"%s\",\"sessionType\":\"WEB\"}", 
                authResponse.getRefreshToken());

        MvcResult refreshResult = mockMvc.perform(post("/auth/refresh")
                .contentType(MediaType.APPLICATION_JSON)
                .content(refreshJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.accessToken").exists())
                .andExpect(jsonPath("$.refreshToken").exists())
                .andExpect(jsonPath("$.user.email").value(testEmail))
                .andExpect(jsonPath("$.user.username").value("testuser"))
                .andReturn();

        // Then
        AuthResponse refreshResponse = objectMapper.readValue(
                refreshResult.getResponse().getContentAsString(), 
                AuthResponse.class);

        assertThat(refreshResponse.getAccessToken()).isNotNull();
        assertThat(refreshResponse.getRefreshToken()).isNotNull();
        assertThat(refreshResponse.getUser().getEmail()).isEqualTo(testEmail);
        assertThat(refreshResponse.getUser().getUsername()).isEqualTo("testuser");
    }

    @Test
    @DisplayName("Should maintain compatibility with logout functionality")
    void shouldMaintainCompatibilityWithLogoutFunctionality() throws Exception {
        // Given - Login to get tokens
        String loginJson = String.format(
                "{\"identifier\":\"%s\",\"password\":\"%s\",\"sessionType\":\"WEB\"}", 
                testEmail, testPassword);

        MvcResult loginResult = mockMvc.perform(post("/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(loginJson))
                .andExpect(status().isOk())
                .andReturn();

        AuthResponse authResponse = objectMapper.readValue(
                loginResult.getResponse().getContentAsString(), 
                AuthResponse.class);

        // When - Logout
        String logoutJson = String.format(
                "{\"refreshToken\":\"%s\",\"sessionType\":\"WEB\"}", 
                authResponse.getRefreshToken());

        mockMvc.perform(post("/auth/logout")
                .contentType(MediaType.APPLICATION_JSON)
                .content(logoutJson))
                .andExpect(status().isOk());

        // Then - Access token should no longer work
        mockMvc.perform(get("/auth/profile")
                .header("Authorization", "Bearer " + authResponse.getAccessToken()))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @DisplayName("Should maintain compatibility with enabled field behavior")
    void shouldMaintainCompatibilityWithEnabledFieldBehavior() throws Exception {
        // Given - User with enabled=false but status=ACTIVE
        testUser.setEnabled(false);
        testUser.setStatus(UserStatus.ACTIVE);
        userRepository.save(testUser);

        // When - Try to login
        String loginJson = String.format(
                "{\"identifier\":\"%s\",\"password\":\"%s\",\"sessionType\":\"WEB\"}", 
                testEmail, testPassword);

        // Then - Should still be able to login (status takes precedence)
        mockMvc.perform(post("/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(loginJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.accessToken").exists())
                .andExpect(jsonPath("$.user.username").value("testuser"));
    }
}
