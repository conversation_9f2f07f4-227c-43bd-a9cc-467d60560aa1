package co.com.gedsys.authentication.integration;

import co.com.gedsys.authentication.entity.Role;
import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.entity.UserStatus;
import co.com.gedsys.authentication.repository.UserRepository;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Integration tests for data migration scenarios.
 * Tests the migration from old schema to new schema with username and status fields.
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
@DisplayName("Data Migration Integration Tests")
class DataMigrationIntegrationTest {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Test
    @DisplayName("Should verify migration creates username and status columns")
    void shouldVerifyMigrationCreatesUsernameAndStatusColumns() {
        // When - Check if columns exist
        String checkUsernameColumn = "SELECT column_name FROM information_schema.columns " +
                "WHERE table_name = 'users' AND column_name = 'username'";
        String checkStatusColumn = "SELECT column_name FROM information_schema.columns " +
                "WHERE table_name = 'users' AND column_name = 'status'";

        List<String> usernameColumns = jdbcTemplate.queryForList(checkUsernameColumn, String.class);
        List<String> statusColumns = jdbcTemplate.queryForList(checkStatusColumn, String.class);

        // Then
        assertThat(usernameColumns).hasSize(1);
        assertThat(statusColumns).hasSize(1);
    }

    @Test
    @DisplayName("Should verify migration creates required indexes")
    void shouldVerifyMigrationCreatesRequiredIndexes() {
        // When - Check if indexes exist
        String checkUsernameIndex = "SELECT indexname FROM pg_indexes " +
                "WHERE tablename = 'users' AND indexname = 'idx_users_username'";
        String checkStatusIndex = "SELECT indexname FROM pg_indexes " +
                "WHERE tablename = 'users' AND indexname = 'idx_users_status'";

        List<String> usernameIndexes = jdbcTemplate.queryForList(checkUsernameIndex, String.class);
        List<String> statusIndexes = jdbcTemplate.queryForList(checkStatusIndex, String.class);

        // Then
        assertThat(usernameIndexes).hasSize(1);
        assertThat(statusIndexes).hasSize(1);
    }

    @Test
    @DisplayName("Should handle existing users without username gracefully")
    void shouldHandleExistingUsersWithoutUsernameGracefully() {
        // Given - Simulate pre-migration user (insert directly to bypass entity validation)
        jdbcTemplate.update(
                "INSERT INTO users (email, password, first_name, last_name, role, enabled, created_at, updated_at, status) " +
                "VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW(), ?)",
                "<EMAIL>", "password", "Legacy", "User", "USER", true, "ACTIVE"
        );

        // When - Find user through repository
        Optional<User> legacyUser = userRepository.findByEmail("<EMAIL>");

        // Then
        assertThat(legacyUser).isPresent();
        assertThat(legacyUser.get().getEmail()).isEqualTo("<EMAIL>");
        assertThat(legacyUser.get().getFirstName()).isEqualTo("Legacy");
        assertThat(legacyUser.get().getLastName()).isEqualTo("User");
        assertThat(legacyUser.get().getStatus()).isEqualTo(UserStatus.ACTIVE);
        // Username might be null for legacy users
        assertThat(legacyUser.get().getUsername()).isNull();
    }

    @Test
    @DisplayName("Should maintain data integrity after migration")
    void shouldMaintainDataIntegrityAfterMigration() {
        // Given - Create users with new schema
        User activeUser = new User();
        activeUser.setEmail("<EMAIL>");
        activeUser.setUsername("activeuser");
        activeUser.setPassword("password");
        activeUser.setFirstName("Active");
        activeUser.setLastName("User");
        activeUser.setRole(Role.USER);
        activeUser.setStatus(UserStatus.ACTIVE);
        activeUser.setEnabled(true);

        User inactiveUser = new User();
        inactiveUser.setEmail("<EMAIL>");
        inactiveUser.setUsername("inactiveuser");
        inactiveUser.setPassword("password");
        inactiveUser.setFirstName("Inactive");
        inactiveUser.setLastName("User");
        inactiveUser.setRole(Role.USER);
        inactiveUser.setStatus(UserStatus.INACTIVE);
        inactiveUser.setEnabled(false);

        // When
        User savedActiveUser = userRepository.save(activeUser);
        User savedInactiveUser = userRepository.save(inactiveUser);

        // Then - Verify data integrity
        assertThat(savedActiveUser.getId()).isNotNull();
        assertThat(savedActiveUser.getCreatedAt()).isNotNull();
        assertThat(savedActiveUser.getUpdatedAt()).isNotNull();
        assertThat(savedActiveUser.getStatus()).isEqualTo(UserStatus.ACTIVE);
        assertThat(savedActiveUser.isActive()).isTrue();

        assertThat(savedInactiveUser.getId()).isNotNull();
        assertThat(savedInactiveUser.getStatus()).isEqualTo(UserStatus.INACTIVE);
        assertThat(savedInactiveUser.isActive()).isFalse();
    }

    @Test
    @DisplayName("Should support queries with new status field")
    void shouldSupportQueriesWithNewStatusField() {
        // Given - Create users with different statuses
        User activeUser = createTestUser("<EMAIL>", "activeuser", UserStatus.ACTIVE);
        User inactiveUser = createTestUser("<EMAIL>", "inactiveuser", UserStatus.INACTIVE);
        User deletedUser = createTestUser("<EMAIL>", "deleteduser", UserStatus.DELETED);

        userRepository.save(activeUser);
        userRepository.save(inactiveUser);
        userRepository.save(deletedUser);

        // When - Query by status
        Pageable pageable = PageRequest.of(0, 10);
        Page<User> activeUsersPage = userRepository.findByStatus(UserStatus.ACTIVE, pageable);
        Page<User> inactiveUsersPage = userRepository.findByStatus(UserStatus.INACTIVE, pageable);
        Page<User> deletedUsersPage = userRepository.findByStatus(UserStatus.DELETED, pageable);

        List<User> activeUsers = activeUsersPage.getContent();
        List<User> inactiveUsers = inactiveUsersPage.getContent();
        List<User> deletedUsers = deletedUsersPage.getContent();

        // Then
        assertThat(activeUsers).hasSize(1);
        assertThat(activeUsers.get(0).getEmail()).isEqualTo("<EMAIL>");

        assertThat(inactiveUsers).hasSize(1);
        assertThat(inactiveUsers.get(0).getEmail()).isEqualTo("<EMAIL>");

        assertThat(deletedUsers).hasSize(1);
        assertThat(deletedUsers.get(0).getEmail()).isEqualTo("<EMAIL>");
    }

    @Test
    @DisplayName("Should support queries with new username field")
    void shouldSupportQueriesWithNewUsernameField() {
        // Given
        User user = createTestUser("<EMAIL>", "testuser", UserStatus.ACTIVE);
        userRepository.save(user);

        // When
        Optional<User> foundByUsername = userRepository.findByUsername("testuser");
        Optional<User> foundByUsernameOrEmail1 = userRepository.findByUsernameOrEmail("testuser");
        Optional<User> foundByUsernameOrEmail2 = userRepository.findByUsernameOrEmail("<EMAIL>");

        // Then
        assertThat(foundByUsername).isPresent();
        assertThat(foundByUsername.get().getEmail()).isEqualTo("<EMAIL>");

        assertThat(foundByUsernameOrEmail1).isPresent();
        assertThat(foundByUsernameOrEmail1.get().getUsername()).isEqualTo("testuser");

        assertThat(foundByUsernameOrEmail2).isPresent();
        assertThat(foundByUsernameOrEmail2.get().getUsername()).isEqualTo("testuser");
    }

    @Test
    @DisplayName("Should maintain backward compatibility with enabled field")
    void shouldMaintainBackwardCompatibilityWithEnabledField() {
        // Given
        User user = createTestUser("<EMAIL>", "compatuser", UserStatus.ACTIVE);
        user.setEnabled(true);
        User savedUser = userRepository.save(user);

        // When - Update enabled field
        savedUser.setEnabled(false);
        User updatedUser = userRepository.save(savedUser);

        // Then - Both enabled and status should be accessible
        assertThat(updatedUser.isEnabled()).isFalse();
        assertThat(updatedUser.getStatus()).isEqualTo(UserStatus.ACTIVE); // Status unchanged
        assertThat(updatedUser.isActive()).isTrue(); // isActive() uses status, not enabled
    }

    @Test
    @DisplayName("Should handle username uniqueness constraints")
    void shouldHandleUsernameUniquenessConstraints() {
        // Given
        User user1 = createTestUser("<EMAIL>", "uniqueuser", UserStatus.ACTIVE);
        userRepository.save(user1);

        User user2 = createTestUser("<EMAIL>", "uniqueuser", UserStatus.ACTIVE);

        // When & Then - Should handle constraint violation gracefully
        try {
            userRepository.save(user2);
            userRepository.flush(); // Force constraint check
        } catch (Exception e) {
            // Expected - username constraint violation
            assertThat(e.getMessage()).containsIgnoringCase("username");
        }
    }

    @Test
    @DisplayName("Should support complex queries with both old and new fields")
    void shouldSupportComplexQueriesWithBothOldAndNewFields() {
        // Given
        User enabledActiveUser = createTestUser("<EMAIL>", "enabledactive", UserStatus.ACTIVE);
        enabledActiveUser.setEnabled(true);

        User disabledActiveUser = createTestUser("<EMAIL>", "disabledactive", UserStatus.ACTIVE);
        disabledActiveUser.setEnabled(false);

        User enabledInactiveUser = createTestUser("<EMAIL>", "enabledinactive", UserStatus.INACTIVE);
        enabledInactiveUser.setEnabled(true);

        userRepository.save(enabledActiveUser);
        userRepository.save(disabledActiveUser);
        userRepository.save(enabledInactiveUser);

        // When - Query with complex conditions
        Pageable pageable = PageRequest.of(0, 10);
        Page<User> activeUsersPage = userRepository.findByStatus(UserStatus.ACTIVE, pageable);
        Page<User> enabledUsersPage = userRepository.findByEnabledTrue(pageable);

        List<User> activeUsers = activeUsersPage.getContent();
        List<User> enabledUsers = enabledUsersPage.getContent();

        // Then
        assertThat(activeUsers).hasSize(2);
        assertThat(enabledUsers).hasSize(2);

        // Verify specific combinations
        assertThat(activeUsers.stream().anyMatch(u -> u.getUsername().equals("enabledactive"))).isTrue();
        assertThat(activeUsers.stream().anyMatch(u -> u.getUsername().equals("disabledactive"))).isTrue();
        assertThat(enabledUsers.stream().anyMatch(u -> u.getUsername().equals("enabledactive"))).isTrue();
        assertThat(enabledUsers.stream().anyMatch(u -> u.getUsername().equals("enabledinactive"))).isTrue();
    }

    private User createTestUser(String email, String username, UserStatus status) {
        User user = new User();
        user.setEmail(email);
        user.setUsername(username);
        user.setPassword("password");
        user.setFirstName("Test");
        user.setLastName("User");
        user.setRole(Role.USER);
        user.setStatus(status);
        user.setEnabled(status == UserStatus.ACTIVE);
        return user;
    }
}
