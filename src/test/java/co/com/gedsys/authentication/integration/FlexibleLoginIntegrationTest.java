package co.com.gedsys.authentication.integration;

import co.com.gedsys.authentication.dto.AuthResponse;
import co.com.gedsys.authentication.dto.LoginRequest;
import co.com.gedsys.authentication.entity.Role;
import co.com.gedsys.authentication.entity.SessionType;
import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.entity.UserStatus;
import co.com.gedsys.authentication.repository.UserRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.transaction.annotation.Transactional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for flexible login functionality (username or email).
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
@DisplayName("Flexible Login Integration Tests")
class FlexibleLoginIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    private User testUser;
    private final String testPassword = "password123";
    private final String testEmail = "<EMAIL>";
    private final String testUsername = "testuser";

    @BeforeEach
    void setUp() {
        userRepository.deleteAll();
        
        // Create test user
        testUser = new User();
        testUser.setEmail(testEmail);
        testUser.setUsername(testUsername);
        testUser.setPassword(passwordEncoder.encode(testPassword));
        testUser.setFirstName("Test");
        testUser.setLastName("User");
        testUser.setRole(Role.USER);
        testUser.setStatus(UserStatus.ACTIVE);
        testUser.setEnabled(true);
        testUser = userRepository.save(testUser);
    }

    @Test
    @DisplayName("Should login successfully with username")
    void shouldLoginSuccessfullyWithUsername() throws Exception {
        // Given
        LoginRequest loginRequest = new LoginRequest();
        loginRequest.setIdentifier(testUsername);
        loginRequest.setPassword(testPassword);
        loginRequest.setSessionType(SessionType.WEB);

        // When
        MvcResult result = mockMvc.perform(post("/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.accessToken").exists())
                .andExpect(jsonPath("$.refreshToken").exists())
                .andExpect(jsonPath("$.user.email").value(testEmail))
                .andExpect(jsonPath("$.user.username").value(testUsername))
                .andExpect(jsonPath("$.user.firstName").value("Test"))
                .andExpect(jsonPath("$.user.lastName").value("User"))
                .andReturn();

        // Then
        AuthResponse response = objectMapper.readValue(
                result.getResponse().getContentAsString(), 
                AuthResponse.class);

        assertThat(response.getAccessToken()).isNotNull();
        assertThat(response.getRefreshToken()).isNotNull();
        assertThat(response.getUser().getEmail()).isEqualTo(testEmail);
        assertThat(response.getUser().getUsername()).isEqualTo(testUsername);
    }

    @Test
    @DisplayName("Should login successfully with email")
    void shouldLoginSuccessfullyWithEmail() throws Exception {
        // Given
        LoginRequest loginRequest = new LoginRequest();
        loginRequest.setIdentifier(testEmail);
        loginRequest.setPassword(testPassword);
        loginRequest.setSessionType(SessionType.WEB);

        // When
        MvcResult result = mockMvc.perform(post("/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.accessToken").exists())
                .andExpect(jsonPath("$.refreshToken").exists())
                .andExpect(jsonPath("$.user.email").value(testEmail))
                .andExpect(jsonPath("$.user.username").value(testUsername))
                .andReturn();

        // Then
        AuthResponse response = objectMapper.readValue(
                result.getResponse().getContentAsString(), 
                AuthResponse.class);

        assertThat(response.getAccessToken()).isNotNull();
        assertThat(response.getRefreshToken()).isNotNull();
        assertThat(response.getUser().getEmail()).isEqualTo(testEmail);
        assertThat(response.getUser().getUsername()).isEqualTo(testUsername);
    }

    @Test
    @DisplayName("Should fail login with wrong password")
    void shouldFailLoginWithWrongPassword() throws Exception {
        // Given
        LoginRequest loginRequest = new LoginRequest();
        loginRequest.setIdentifier(testUsername);
        loginRequest.setPassword("wrongpassword");
        loginRequest.setSessionType(SessionType.WEB);

        // When & Then
        mockMvc.perform(post("/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.error").value("INVALID_CREDENTIALS"));
    }

    @Test
    @DisplayName("Should fail login with non-existent identifier")
    void shouldFailLoginWithNonExistentIdentifier() throws Exception {
        // Given
        LoginRequest loginRequest = new LoginRequest();
        loginRequest.setIdentifier("<EMAIL>");
        loginRequest.setPassword(testPassword);
        loginRequest.setSessionType(SessionType.WEB);

        // When & Then
        mockMvc.perform(post("/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.error").value("INVALID_CREDENTIALS"));
    }

    @Test
    @DisplayName("Should fail login with deleted user")
    void shouldFailLoginWithDeletedUser() throws Exception {
        // Given - Mark user as deleted
        testUser.setStatus(UserStatus.DELETED);
        userRepository.save(testUser);

        LoginRequest loginRequest = new LoginRequest();
        loginRequest.setIdentifier(testUsername);
        loginRequest.setPassword(testPassword);
        loginRequest.setSessionType(SessionType.WEB);

        // When & Then
        mockMvc.perform(post("/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.error").value("USER_NOT_FOUND"));
    }

    @Test
    @DisplayName("Should fail login with inactive user")
    void shouldFailLoginWithInactiveUser() throws Exception {
        // Given - Mark user as inactive
        testUser.setStatus(UserStatus.INACTIVE);
        userRepository.save(testUser);

        LoginRequest loginRequest = new LoginRequest();
        loginRequest.setIdentifier(testEmail);
        loginRequest.setPassword(testPassword);
        loginRequest.setSessionType(SessionType.WEB);

        // When & Then
        mockMvc.perform(post("/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isForbidden())
                .andExpect(jsonPath("$.error").value("USER_DISABLED"));
    }

    @Test
    @DisplayName("Should prioritize username over email when both match")
    void shouldPrioritizeUsernameOverEmailWhenBothMatch() throws Exception {
        // Given - Create another user with email that matches first user's username
        User emailUser = new User();
        emailUser.setEmail(testUsername); // Email same as first user's username
        emailUser.setUsername("emailuser");
        emailUser.setPassword(passwordEncoder.encode("differentpassword"));
        emailUser.setFirstName("Email");
        emailUser.setLastName("User");
        emailUser.setRole(Role.USER);
        emailUser.setStatus(UserStatus.ACTIVE);
        emailUser.setEnabled(true);
        userRepository.save(emailUser);

        LoginRequest loginRequest = new LoginRequest();
        loginRequest.setIdentifier(testUsername); // This matches first user's username and second user's email
        loginRequest.setPassword(testPassword); // First user's password
        loginRequest.setSessionType(SessionType.WEB);

        // When
        MvcResult result = mockMvc.perform(post("/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.user.username").value(testUsername))
                .andExpect(jsonPath("$.user.email").value(testEmail))
                .andReturn();

        // Then - Should authenticate as first user (username match takes priority)
        AuthResponse response = objectMapper.readValue(
                result.getResponse().getContentAsString(), 
                AuthResponse.class);

        assertThat(response.getUser().getUsername()).isEqualTo(testUsername);
        assertThat(response.getUser().getEmail()).isEqualTo(testEmail);
        assertThat(response.getUser().getFirstName()).isEqualTo("Test");
    }

    @Test
    @DisplayName("Should maintain backward compatibility with old login format")
    void shouldMaintainBackwardCompatibilityWithOldLoginFormat() throws Exception {
        // Given - Old format login request (using email field)
        String oldFormatJson = String.format(
                "{\"email\":\"%s\",\"password\":\"%s\",\"sessionType\":\"WEB\"}", 
                testEmail, testPassword);

        // When
        MvcResult result = mockMvc.perform(post("/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(oldFormatJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.accessToken").exists())
                .andExpect(jsonPath("$.user.email").value(testEmail))
                .andExpect(jsonPath("$.user.username").value(testUsername))
                .andReturn();

        // Then
        AuthResponse response = objectMapper.readValue(
                result.getResponse().getContentAsString(), 
                AuthResponse.class);

        assertThat(response.getAccessToken()).isNotNull();
        assertThat(response.getUser().getEmail()).isEqualTo(testEmail);
        assertThat(response.getUser().getUsername()).isEqualTo(testUsername);
    }
}
