package co.com.gedsys.authentication.integration;

import co.com.gedsys.authentication.dto.LoginRequest;
import co.com.gedsys.authentication.dto.AuthResponse;
import co.com.gedsys.authentication.entity.SessionType;
import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.repository.UserRepository;
import co.com.gedsys.authentication.service.AuthenticationService;
import co.com.gedsys.authentication.service.JwtService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.transaction.annotation.Transactional;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration test to verify that access tokens are properly invalidated
 * when a user logs in multiple times (single session policy).
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
class SessionInvalidationIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private AuthenticationService authenticationService;

    @Autowired
    private JwtService jwtService;

    @Autowired
    private UserRepository userRepository;

    private User testUser;

    @BeforeEach
    void setUp() {
        // Create test user
        testUser = new User();
        testUser.setEmail("<EMAIL>");
        testUser.setPassword("$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iYqiSfFGjO6NaaJHL/.Qk3eZXFna"); // "password123"
        testUser.setFirstName("Test");
        testUser.setLastName("User");
        testUser.setEnabled(true);
        testUser = userRepository.save(testUser);
    }

    @Test
    @DisplayName("Should invalidate previous access token when user logs in again")
    void shouldInvalidatePreviousAccessTokenOnNewLogin() throws Exception {
        // First login
        LoginRequest firstLogin = new LoginRequest();
        firstLogin.setEmail("<EMAIL>");
        firstLogin.setPassword("password123");
        firstLogin.setSessionType(SessionType.WEB);

        MvcResult firstLoginResult = mockMvc.perform(post("/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(firstLogin)))
                .andExpect(status().isOk())
                .andReturn();

        AuthResponse firstResponse = objectMapper.readValue(
                firstLoginResult.getResponse().getContentAsString(), 
                AuthResponse.class);
        String firstAccessToken = firstResponse.getAccessToken();

        // Verify first token works
        mockMvc.perform(get("/auth/profile")
                .header("Authorization", "Bearer " + firstAccessToken))
                .andExpect(status().isOk());

        // Second login (should invalidate first token)
        LoginRequest secondLogin = new LoginRequest();
        secondLogin.setEmail("<EMAIL>");
        secondLogin.setPassword("password123");
        secondLogin.setSessionType(SessionType.WEB);

        MvcResult secondLoginResult = mockMvc.perform(post("/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(secondLogin)))
                .andExpect(status().isOk())
                .andReturn();

        AuthResponse secondResponse = objectMapper.readValue(
                secondLoginResult.getResponse().getContentAsString(), 
                AuthResponse.class);
        String secondAccessToken = secondResponse.getAccessToken();

        // Verify second token works
        mockMvc.perform(get("/auth/profile")
                .header("Authorization", "Bearer " + secondAccessToken))
                .andExpect(status().isOk());

        // Verify first token is now invalid (this is the key test)
        mockMvc.perform(get("/auth/profile")
                .header("Authorization", "Bearer " + firstAccessToken))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @DisplayName("Should allow concurrent web and mobile sessions")
    void shouldAllowConcurrentWebAndMobileSessions() throws Exception {
        // Web login
        LoginRequest webLogin = new LoginRequest();
        webLogin.setEmail("<EMAIL>");
        webLogin.setPassword("password123");
        webLogin.setSessionType(SessionType.WEB);

        MvcResult webLoginResult = mockMvc.perform(post("/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(webLogin)))
                .andExpect(status().isOk())
                .andReturn();

        AuthResponse webResponse = objectMapper.readValue(
                webLoginResult.getResponse().getContentAsString(), 
                AuthResponse.class);
        String webAccessToken = webResponse.getAccessToken();

        // Mobile login
        LoginRequest mobileLogin = new LoginRequest();
        mobileLogin.setEmail("<EMAIL>");
        mobileLogin.setPassword("password123");
        mobileLogin.setSessionType(SessionType.MOBILE);

        MvcResult mobileLoginResult = mockMvc.perform(post("/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mobileLogin)))
                .andExpect(status().isOk())
                .andReturn();

        AuthResponse mobileResponse = objectMapper.readValue(
                mobileLoginResult.getResponse().getContentAsString(), 
                AuthResponse.class);
        String mobileAccessToken = mobileResponse.getAccessToken();

        // Both tokens should work simultaneously
        mockMvc.perform(get("/auth/profile")
                .header("Authorization", "Bearer " + webAccessToken))
                .andExpect(status().isOk());

        mockMvc.perform(get("/auth/profile")
                .header("Authorization", "Bearer " + mobileAccessToken))
                .andExpect(status().isOk());
    }
}
