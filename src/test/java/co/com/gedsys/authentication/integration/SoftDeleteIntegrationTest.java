package co.com.gedsys.authentication.integration;

import co.com.gedsys.authentication.dto.AuthResponse;
import co.com.gedsys.authentication.dto.LoginRequest;
import co.com.gedsys.authentication.entity.*;
import co.com.gedsys.authentication.repository.RefreshTokenRepository;
import co.com.gedsys.authentication.repository.UserRepository;
import co.com.gedsys.authentication.service.UserService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for soft delete functionality.
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
@DisplayName("Soft Delete Integration Tests")
class SoftDeleteIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RefreshTokenRepository refreshTokenRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private PasswordEncoder passwordEncoder;

    private User testUser;
    private final String testPassword = "password123";
    private final String testEmail = "<EMAIL>";
    private final String testUsername = "testuser";

    @BeforeEach
    void setUp() {
        userRepository.deleteAll();
        refreshTokenRepository.deleteAll();
        
        // Create test user
        testUser = new User();
        testUser.setEmail(testEmail);
        testUser.setUsername(testUsername);
        testUser.setPassword(passwordEncoder.encode(testPassword));
        testUser.setFirstName("Test");
        testUser.setLastName("User");
        testUser.setRole(Role.USER);
        testUser.setStatus(UserStatus.ACTIVE);
        testUser.setEnabled(true);
        testUser = userRepository.save(testUser);
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("Should soft delete user and invalidate sessions")
    void shouldSoftDeleteUserAndInvalidateSessions() throws Exception {
        // Given - Create active session for user
        LoginRequest loginRequest = new LoginRequest();
        loginRequest.setIdentifier(testEmail);
        loginRequest.setPassword(testPassword);
        loginRequest.setSessionType(SessionType.WEB);

        MvcResult loginResult = mockMvc.perform(post("/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isOk())
                .andReturn();

        AuthResponse authResponse = objectMapper.readValue(
                loginResult.getResponse().getContentAsString(), 
                AuthResponse.class);

        // Verify session exists
        Optional<RefreshToken> refreshToken = refreshTokenRepository.findByUserAndSessionType(testUser, SessionType.WEB);
        assertThat(refreshToken).isPresent();

        // When - Soft delete user
        mockMvc.perform(delete("/admin/users/{id}", testUser.getId()))
                .andExpect(status().isNoContent());

        // Then - User should be marked as deleted
        Optional<User> deletedUser = userRepository.findById(testUser.getId());
        assertThat(deletedUser).isPresent();
        assertThat(deletedUser.get().getStatus()).isEqualTo(UserStatus.DELETED);

        // Session should be invalidated
        Optional<RefreshToken> invalidatedToken = refreshTokenRepository.findByUserAndSessionType(testUser, SessionType.WEB);
        assertThat(invalidatedToken).isEmpty();

        // Access token should no longer work
        mockMvc.perform(get("/auth/profile")
                .header("Authorization", "Bearer " + authResponse.getAccessToken()))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @DisplayName("Should prevent login after soft delete")
    void shouldPreventLoginAfterSoftDelete() throws Exception {
        // Given - Soft delete user
        userService.softDeleteUser(testUser.getId());

        LoginRequest loginRequest = new LoginRequest();
        loginRequest.setIdentifier(testUsername);
        loginRequest.setPassword(testPassword);
        loginRequest.setSessionType(SessionType.WEB);

        // When & Then - Login should fail
        mockMvc.perform(post("/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.error").value("USER_NOT_FOUND"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("Should not find deleted user in admin user list")
    void shouldNotFindDeletedUserInAdminUserList() throws Exception {
        // Given - Soft delete user
        userService.softDeleteUser(testUser.getId());

        // When - Get users list
        mockMvc.perform(get("/admin/users"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content[?(@.id == " + testUser.getId() + ")]").doesNotExist());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("Should not find deleted user by ID in admin endpoint")
    void shouldNotFindDeletedUserByIdInAdminEndpoint() throws Exception {
        // Given - Soft delete user
        userService.softDeleteUser(testUser.getId());

        // When & Then - Get user by ID should return 404
        mockMvc.perform(get("/admin/users/{id}", testUser.getId()))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.error").value("USER_NOT_FOUND"));
    }

    @Test
    @DisplayName("Should prevent creating new user with same email as deleted user")
    void shouldPreventCreatingNewUserWithSameEmailAsDeletedUser() throws Exception {
        // Given - Soft delete user
        userService.softDeleteUser(testUser.getId());

        // When - Try to create new user with same email
        String createUserJson = String.format(
                "{\"email\":\"%s\",\"password\":\"newpassword\",\"firstName\":\"New\",\"lastName\":\"User\",\"role\":\"USER\",\"enabled\":true}",
                testEmail);

        // Then - Should fail with conflict
        mockMvc.perform(post("/admin/users")
                .contentType(MediaType.APPLICATION_JSON)
                .content(createUserJson)
                .with(request -> {
                    request.setAttribute("org.springframework.security.core.context.SecurityContext", 
                            org.springframework.security.core.context.SecurityContextHolder.getContext());
                    return request;
                }))
                .andExpect(status().isConflict())
                .andExpect(jsonPath("$.error").value("EMAIL_ALREADY_EXISTS"));
    }

    @Test
    @DisplayName("Should prevent creating new user with same username as deleted user")
    void shouldPreventCreatingNewUserWithSameUsernameAsDeletedUser() throws Exception {
        // Given - Soft delete user
        userService.softDeleteUser(testUser.getId());

        // When - Try to create new user with same username
        String createUserJson = String.format(
                "{\"email\":\"<EMAIL>\",\"username\":\"%s\",\"password\":\"newpassword\",\"firstName\":\"New\",\"lastName\":\"User\",\"role\":\"USER\",\"enabled\":true}",
                testUsername);

        // Then - Should fail with conflict
        mockMvc.perform(post("/admin/users")
                .contentType(MediaType.APPLICATION_JSON)
                .content(createUserJson)
                .with(request -> {
                    request.setAttribute("org.springframework.security.core.context.SecurityContext", 
                            org.springframework.security.core.context.SecurityContextHolder.getContext());
                    return request;
                }))
                .andExpect(status().isConflict())
                .andExpect(jsonPath("$.error").value("USERNAME_ALREADY_EXISTS"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("Should handle multiple session invalidation on soft delete")
    void shouldHandleMultipleSessionInvalidationOnSoftDelete() throws Exception {
        // Given - Create multiple sessions for user
        LoginRequest webLogin = new LoginRequest();
        webLogin.setIdentifier(testEmail);
        webLogin.setPassword(testPassword);
        webLogin.setSessionType(SessionType.WEB);

        LoginRequest mobileLogin = new LoginRequest();
        mobileLogin.setIdentifier(testEmail);
        mobileLogin.setPassword(testPassword);
        mobileLogin.setSessionType(SessionType.MOBILE);

        // Create web session
        mockMvc.perform(post("/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(webLogin)))
                .andExpect(status().isOk());

        // Create mobile session
        mockMvc.perform(post("/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mobileLogin)))
                .andExpect(status().isOk());

        // Verify both sessions exist
        assertThat(refreshTokenRepository.findByUserAndSessionType(testUser, SessionType.WEB)).isPresent();
        assertThat(refreshTokenRepository.findByUserAndSessionType(testUser, SessionType.MOBILE)).isPresent();

        // When - Soft delete user
        mockMvc.perform(delete("/admin/users/{id}", testUser.getId()))
                .andExpect(status().isNoContent());

        // Then - Both sessions should be invalidated
        assertThat(refreshTokenRepository.findByUserAndSessionType(testUser, SessionType.WEB)).isEmpty();
        assertThat(refreshTokenRepository.findByUserAndSessionType(testUser, SessionType.MOBILE)).isEmpty();
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("Should return 404 when trying to delete non-existent user")
    void shouldReturn404WhenTryingToDeleteNonExistentUser() throws Exception {
        // Given - Non-existent user ID
        Long nonExistentId = 99999L;

        // When & Then
        mockMvc.perform(delete("/admin/users/{id}", nonExistentId))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.error").value("USER_NOT_FOUND"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("Should return 404 when trying to delete already deleted user")
    void shouldReturn404WhenTryingToDeleteAlreadyDeletedUser() throws Exception {
        // Given - Already deleted user
        userService.softDeleteUser(testUser.getId());

        // When & Then - Try to delete again
        mockMvc.perform(delete("/admin/users/{id}", testUser.getId()))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.error").value("USER_NOT_FOUND"));
    }
}
