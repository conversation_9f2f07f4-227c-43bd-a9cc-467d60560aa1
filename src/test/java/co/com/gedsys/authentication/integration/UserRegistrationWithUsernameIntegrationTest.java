package co.com.gedsys.authentication.integration;

import co.com.gedsys.authentication.dto.CreateUserRequest;
import co.com.gedsys.authentication.dto.UserProfileResponse;
import co.com.gedsys.authentication.entity.Role;
import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.entity.UserStatus;
import co.com.gedsys.authentication.repository.UserRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for user registration with automatic username generation.
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
@DisplayName("User Registration with Username Integration Tests")
class UserRegistrationWithUsernameIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private UserRepository userRepository;

    @BeforeEach
    void setUp() {
        userRepository.deleteAll();
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("Should create user with auto-generated username when not provided")
    void shouldCreateUserWithAutoGeneratedUsernameWhenNotProvided() throws Exception {
        // Given
        CreateUserRequest request = new CreateUserRequest();
        request.setEmail("<EMAIL>");
        request.setPassword("password123");
        request.setFirstName("John");
        request.setLastName("Doe");
        request.setRole(Role.USER);
        request.setEnabled(true);

        // When
        MvcResult result = mockMvc.perform(post("/admin/users")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.firstName").value("John"))
                .andExpect(jsonPath("$.lastName").value("Doe"))
                .andExpect(jsonPath("$.username").exists())
                .andExpect(jsonPath("$.username").value("jdoe"))
                .andReturn();

        // Then
        UserProfileResponse response = objectMapper.readValue(
                result.getResponse().getContentAsString(), 
                UserProfileResponse.class);

        assertThat(response.getUsername()).isEqualTo("jdoe");
        assertThat(response.getEmail()).isEqualTo("<EMAIL>");

        // Verify in database
        Optional<User> savedUser = userRepository.findByEmail("<EMAIL>");
        assertThat(savedUser).isPresent();
        assertThat(savedUser.get().getUsername()).isEqualTo("jdoe");
        assertThat(savedUser.get().getStatus()).isEqualTo(UserStatus.ACTIVE);
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("Should handle username conflicts by generating incremental usernames")
    void shouldHandleUsernameConflictsByGeneratingIncrementalUsernames() throws Exception {
        // Given - Create first user with same name pattern
        User existingUser = new User();
        existingUser.setEmail("<EMAIL>");
        existingUser.setPassword("password");
        existingUser.setFirstName("John");
        existingUser.setLastName("Doe");
        existingUser.setUsername("jdoe");
        existingUser.setStatus(UserStatus.ACTIVE);
        existingUser.setRole(Role.USER);
        userRepository.save(existingUser);

        CreateUserRequest request = new CreateUserRequest();
        request.setEmail("<EMAIL>");
        request.setPassword("password123");
        request.setFirstName("John");
        request.setLastName("Doe");
        request.setRole(Role.USER);
        request.setEnabled(true);

        // When
        MvcResult result = mockMvc.perform(post("/admin/users")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.username").value("jdoe2"))
                .andReturn();

        // Then
        UserProfileResponse response = objectMapper.readValue(
                result.getResponse().getContentAsString(), 
                UserProfileResponse.class);

        assertThat(response.getUsername()).isEqualTo("jdoe2");

        // Verify both users exist in database
        Optional<User> firstUser = userRepository.findByUsername("jdoe");
        Optional<User> secondUser = userRepository.findByUsername("jdoe2");
        assertThat(firstUser).isPresent();
        assertThat(secondUser).isPresent();
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("Should handle special characters in names for username generation")
    void shouldHandleSpecialCharactersInNamesForUsernameGeneration() throws Exception {
        // Given
        CreateUserRequest request = new CreateUserRequest();
        request.setEmail("<EMAIL>");
        request.setPassword("password123");
        request.setFirstName("María José");
        request.setLastName("García-López");
        request.setRole(Role.USER);
        request.setEnabled(true);

        // When
        MvcResult result = mockMvc.perform(post("/admin/users")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.username").exists())
                .andReturn();

        // Then
        UserProfileResponse response = objectMapper.readValue(
                result.getResponse().getContentAsString(), 
                UserProfileResponse.class);

        // Username should be sanitized (no special characters)
        assertThat(response.getUsername()).matches("^[a-z0-9]+$");
        assertThat(response.getUsername()).startsWith("m");
        assertThat(response.getUsername()).contains("garcia");

        // Verify in database
        Optional<User> savedUser = userRepository.findByEmail("<EMAIL>");
        assertThat(savedUser).isPresent();
        assertThat(savedUser.get().getUsername()).isEqualTo(response.getUsername());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("Should handle missing first or last name gracefully")
    void shouldHandleMissingFirstOrLastNameGracefully() throws Exception {
        // Given - User with only first name
        CreateUserRequest request = new CreateUserRequest();
        request.setEmail("<EMAIL>");
        request.setPassword("password123");
        request.setFirstName("Madonna");
        request.setLastName(null);
        request.setRole(Role.USER);
        request.setEnabled(true);

        // When
        MvcResult result = mockMvc.perform(post("/admin/users")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.username").exists())
                .andReturn();

        // Then
        UserProfileResponse response = objectMapper.readValue(
                result.getResponse().getContentAsString(), 
                UserProfileResponse.class);

        // Username should be generated from available name parts
        assertThat(response.getUsername()).isNotNull();
        assertThat(response.getUsername()).isNotEmpty();

        // Verify in database
        Optional<User> savedUser = userRepository.findByEmail("<EMAIL>");
        assertThat(savedUser).isPresent();
        assertThat(savedUser.get().getUsername()).isEqualTo(response.getUsername());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    @DisplayName("Should include username in all user response DTOs")
    void shouldIncludeUsernameInAllUserResponseDTOs() throws Exception {
        // Given
        CreateUserRequest request = new CreateUserRequest();
        request.setEmail("<EMAIL>");
        request.setPassword("password123");
        request.setFirstName("DTO");
        request.setLastName("Test");
        request.setRole(Role.ADMIN);
        request.setEnabled(true);

        // When
        MvcResult result = mockMvc.perform(post("/admin/users")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.username").exists())
                .andExpect(jsonPath("$.firstName").value("DTO"))
                .andExpect(jsonPath("$.lastName").value("Test"))
                .andExpect(jsonPath("$.role").value("ADMIN"))
                .andExpect(jsonPath("$.createdAt").exists())
                .andReturn();

        // Then
        UserProfileResponse response = objectMapper.readValue(
                result.getResponse().getContentAsString(), 
                UserProfileResponse.class);

        // Verify all expected fields are present
        assertThat(response.getId()).isNotNull();
        assertThat(response.getEmail()).isEqualTo("<EMAIL>");
        assertThat(response.getUsername()).isNotNull();
        assertThat(response.getFirstName()).isEqualTo("DTO");
        assertThat(response.getLastName()).isEqualTo("Test");
        assertThat(response.getRole()).isEqualTo(Role.ADMIN);
        assertThat(response.getCreatedAt()).isNotNull();
    }
}
