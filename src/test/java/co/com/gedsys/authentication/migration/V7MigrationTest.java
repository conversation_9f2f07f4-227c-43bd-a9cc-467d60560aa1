package co.com.gedsys.authentication.migration;

import co.com.gedsys.authentication.entity.Role;
import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.entity.UserStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Unit test for V7 migration that adds username and status fields.
 * Verifies that the migration SQL script is valid and the User entity supports new fields.
 */
@DisplayName("V7 Migration Tests")
class V7MigrationTest {

    @Test
    @DisplayName("Should verify migration SQL file exists and contains required statements")
    void shouldVerifyMigrationSqlFileExistsAndContainsRequiredStatements() throws IOException {
        // Given - Path to the migration file
        Path migrationPath = Paths.get("src/main/resources/db/migration/V7__Add_username_and_status_fields.sql");

        // When - Read the migration file
        String migrationContent = Files.readString(migrationPath);

        // Then - Verify the migration contains required SQL statements
        assertThat(migrationContent).contains("ALTER TABLE users ADD COLUMN username VARCHAR(100)");
        assertThat(migrationContent).contains("ALTER TABLE users ADD COLUMN status VARCHAR(20)");
        assertThat(migrationContent).contains("CREATE INDEX idx_users_username ON users(username)");
        assertThat(migrationContent).contains("CREATE INDEX idx_users_status ON users(status)");
        assertThat(migrationContent).contains("UPDATE users SET status = 'ACTIVE' WHERE enabled = true");
        assertThat(migrationContent).contains("UPDATE users SET status = 'INACTIVE' WHERE enabled = false");
    }

    @Test
    @DisplayName("Should verify User entity supports new fields")
    void shouldVerifyUserEntitySupportsNewFields() {
        // Given - Create a test user
        User testUser = new User();
        testUser.setEmail("<EMAIL>");
        testUser.setPassword("password123");
        testUser.setFirstName("Test");
        testUser.setLastName("User");
        testUser.setRole(Role.USER);

        // When - Set new fields
        testUser.setUsername("testuser");
        testUser.setStatus(UserStatus.ACTIVE);

        // Then - Verify the new fields are properly set
        assertThat(testUser.getUsername()).isEqualTo("testuser");
        assertThat(testUser.getStatus()).isEqualTo(UserStatus.ACTIVE);
        assertThat(testUser.isActive()).isTrue();
    }

    @Test
    @DisplayName("Should support all UserStatus values")
    void shouldSupportAllUserStatusValues() {
        // Test ACTIVE status
        User activeUser = createTestUser("<EMAIL>", UserStatus.ACTIVE);
        assertThat(activeUser.getStatus()).isEqualTo(UserStatus.ACTIVE);
        assertThat(activeUser.isActive()).isTrue();

        // Test INACTIVE status
        User inactiveUser = createTestUser("<EMAIL>", UserStatus.INACTIVE);
        assertThat(inactiveUser.getStatus()).isEqualTo(UserStatus.INACTIVE);
        assertThat(inactiveUser.isActive()).isFalse();

        // Test DELETED status
        User deletedUser = createTestUser("<EMAIL>", UserStatus.DELETED);
        assertThat(deletedUser.getStatus()).isEqualTo(UserStatus.DELETED);
        assertThat(deletedUser.isActive()).isFalse();
    }

    @Test
    @DisplayName("Should allow username to be set and retrieved")
    void shouldAllowUsernameToBeSetAndRetrieved() {
        // Given
        User user = createTestUser("<EMAIL>", UserStatus.ACTIVE);

        // When
        user.setUsername("testusername");

        // Then
        assertThat(user.getUsername()).isEqualTo("testusername");
    }

    @Test
    @DisplayName("Should maintain backward compatibility with enabled field")
    void shouldMaintainBackwardCompatibilityWithEnabledField() {
        // Given
        User user = createTestUser("<EMAIL>", UserStatus.ACTIVE);

        // When
        user.setEnabled(false);

        // Then - Both old and new fields should work
        assertThat(user.isEnabled()).isFalse();
        assertThat(user.getStatus()).isEqualTo(UserStatus.ACTIVE);
        assertThat(user.isActive()).isTrue(); // Status takes precedence for isActive()
    }

    private User createTestUser(String email, UserStatus status) {
        User user = new User();
        user.setEmail(email);
        user.setPassword("password123");
        user.setFirstName("Test");
        user.setLastName("User");
        user.setRole(Role.USER);
        user.setStatus(status);
        user.setEnabled(true);
        return user;
    }
}
