package co.com.gedsys.authentication.repository;

import co.com.gedsys.authentication.entity.Role;
import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.entity.UserStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Unit tests for enhanced UserRepository methods.
 * Tests the new methods added for username and status functionality.
 */
@DisplayName("UserRepository Enhanced Methods Tests")
class UserRepositoryEnhancedTest {

    private final UserRepository userRepository = mock(UserRepository.class);

    @Test
    @DisplayName("Should find user by username")
    void shouldFindUserByUsername() {
        // Given
        User expectedUser = createTestUser("testuser", "<EMAIL>", UserStatus.ACTIVE);
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(expectedUser));

        // When
        Optional<User> result = userRepository.findByUsername("testuser");

        // Then
        assertThat(result).isPresent();
        assertThat(result.get().getUsername()).isEqualTo("testuser");
        assertThat(result.get().getEmail()).isEqualTo("<EMAIL>");
    }

    @Test
    @DisplayName("Should return empty when username not found")
    void shouldReturnEmptyWhenUsernameNotFound() {
        // Given
        when(userRepository.findByUsername("nonexistent")).thenReturn(Optional.empty());

        // When
        Optional<User> result = userRepository.findByUsername("nonexistent");

        // Then
        assertThat(result).isEmpty();
    }

    @Test
    @DisplayName("Should find user by username or email")
    void shouldFindUserByUsernameOrEmail() {
        // Given
        User expectedUser = createTestUser("testuser", "<EMAIL>", UserStatus.ACTIVE);
        when(userRepository.findByUsernameOrEmail("testuser")).thenReturn(Optional.of(expectedUser));
        when(userRepository.findByUsernameOrEmail("<EMAIL>")).thenReturn(Optional.of(expectedUser));

        // When
        Optional<User> resultByUsername = userRepository.findByUsernameOrEmail("testuser");
        Optional<User> resultByEmail = userRepository.findByUsernameOrEmail("<EMAIL>");

        // Then
        assertThat(resultByUsername).isPresent();
        assertThat(resultByUsername.get().getUsername()).isEqualTo("testuser");
        
        assertThat(resultByEmail).isPresent();
        assertThat(resultByEmail.get().getEmail()).isEqualTo("<EMAIL>");
    }

    @Test
    @DisplayName("Should check if username exists")
    void shouldCheckIfUsernameExists() {
        // Given
        when(userRepository.existsByUsername("existinguser")).thenReturn(true);
        when(userRepository.existsByUsername("nonexistentuser")).thenReturn(false);

        // When & Then
        assertThat(userRepository.existsByUsername("existinguser")).isTrue();
        assertThat(userRepository.existsByUsername("nonexistentuser")).isFalse();
    }

    @Test
    @DisplayName("Should check if username exists including deleted users")
    void shouldCheckIfUsernameExistsIncludingDeleted() {
        // Given
        when(userRepository.existsByUsernameIncludingDeleted("deleteduser")).thenReturn(true);
        when(userRepository.existsByUsernameIncludingDeleted("newuser")).thenReturn(false);

        // When & Then
        assertThat(userRepository.existsByUsernameIncludingDeleted("deleteduser")).isTrue();
        assertThat(userRepository.existsByUsernameIncludingDeleted("newuser")).isFalse();
    }

    @Test
    @DisplayName("Should check if email exists including deleted users")
    void shouldCheckIfEmailExistsIncludingDeleted() {
        // Given
        when(userRepository.existsByEmailIncludingDeleted("<EMAIL>")).thenReturn(true);
        when(userRepository.existsByEmailIncludingDeleted("<EMAIL>")).thenReturn(false);

        // When & Then
        assertThat(userRepository.existsByEmailIncludingDeleted("<EMAIL>")).isTrue();
        assertThat(userRepository.existsByEmailIncludingDeleted("<EMAIL>")).isFalse();
    }

    @Test
    @DisplayName("Should find users by status with pagination")
    void shouldFindUsersByStatusWithPagination() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        Page<User> mockPage = mock(Page.class);
        when(userRepository.findByStatus(UserStatus.ACTIVE, pageable)).thenReturn(mockPage);

        // When
        Page<User> result = userRepository.findByStatus(UserStatus.ACTIVE, pageable);

        // Then
        assertThat(result).isEqualTo(mockPage);
    }

    @Test
    @DisplayName("Should find users with enhanced filters including status")
    void shouldFindUsersWithEnhancedFiltersIncludingStatus() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        Page<User> mockPage = mock(Page.class);
        when(userRepository.findUsersWithFilters(
                eq("test"), 
                eq(true), 
                eq(Role.USER), 
                eq(UserStatus.ACTIVE), 
                eq(pageable)
        )).thenReturn(mockPage);

        // When
        Page<User> result = userRepository.findUsersWithFilters(
                "test", true, Role.USER, UserStatus.ACTIVE, pageable
        );

        // Then
        assertThat(result).isEqualTo(mockPage);
    }

    @Test
    @DisplayName("Should maintain backward compatibility with legacy findUsersWithFilters")
    void shouldMaintainBackwardCompatibilityWithLegacyFindUsersWithFilters() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        Page<User> mockPage = mock(Page.class);
        when(userRepository.findUsersWithFilters(
                eq("test"), 
                eq(true), 
                eq(Role.USER), 
                eq(pageable)
        )).thenReturn(mockPage);

        // When
        Page<User> result = userRepository.findUsersWithFilters(
                "test", true, Role.USER, pageable
        );

        // Then
        assertThat(result).isEqualTo(mockPage);
    }

    private User createTestUser(String username, String email, UserStatus status) {
        User user = new User();
        user.setId(1L);
        user.setUsername(username);
        user.setEmail(email);
        user.setPassword("password123");
        user.setFirstName("Test");
        user.setLastName("User");
        user.setRole(Role.USER);
        user.setStatus(status);
        user.setEnabled(true);
        return user;
    }
}
