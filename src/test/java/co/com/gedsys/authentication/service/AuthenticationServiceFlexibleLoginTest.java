package co.com.gedsys.authentication.service;

import co.com.gedsys.authentication.config.JwtProperties;
import co.com.gedsys.authentication.dto.AuthResponse;
import co.com.gedsys.authentication.dto.LoginRequest;
import co.com.gedsys.authentication.entity.*;
import co.com.gedsys.authentication.exception.InvalidCredentialsException;
import co.com.gedsys.authentication.exception.UserNotFoundException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Unit tests for AuthenticationService flexible login functionality.
 * Tests login with both username and email identifiers.
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("AuthenticationService - Flexible Login Tests")
class AuthenticationServiceFlexibleLoginTest {

    @Mock
    private UserService userService;

    @Mock
    private JwtService jwtService;

    @Mock
    private RefreshTokenService refreshTokenService;

    @Mock
    private PushTokenService pushTokenService;

    @Mock
    private JwtProperties jwtProperties;

    @InjectMocks
    private AuthenticationService authenticationService;

    private User testUser;
    private RefreshToken testRefreshToken;

    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setId(1L);
        testUser.setFirstName("John");
        testUser.setLastName("Doe");
        testUser.setEmail("<EMAIL>");
        testUser.setUsername("jdoe");
        testUser.setPassword("encodedPassword123");
        testUser.setRole(Role.USER);
        testUser.setEnabled(true);
        testUser.setStatus(UserStatus.ACTIVE);
        testUser.setCreatedAt(LocalDateTime.now());
        testUser.setUpdatedAt(LocalDateTime.now());

        testRefreshToken = new RefreshToken();
        testRefreshToken.setId(1L);
        testRefreshToken.setToken("refresh-token-123");
        testRefreshToken.setUser(testUser);
        testRefreshToken.setSessionType(SessionType.WEB);
        testRefreshToken.setSessionId("session-123");
        testRefreshToken.setExpiryDate(LocalDateTime.now().plusDays(7));
    }

    @Nested
    @DisplayName("Login with Email Tests")
    class LoginWithEmailTests {

        @Test
        @DisplayName("Should login successfully with email identifier")
        void shouldLoginSuccessfullyWithEmail() {
            // Given
            LoginRequest loginRequest = new LoginRequest();
            loginRequest.setIdentifier("<EMAIL>");
            loginRequest.setPassword("password123");
            loginRequest.setSessionType(SessionType.WEB);

            when(userService.validateUserCredentials("<EMAIL>", "password123"))
                    .thenReturn(testUser);
            when(refreshTokenService.hasValidTokenForSessionType(testUser, SessionType.WEB))
                    .thenReturn(false);
            when(refreshTokenService.generateRefreshToken(testUser, SessionType.WEB))
                    .thenReturn(testRefreshToken);
            when(jwtService.generateAccessToken(testUser, SessionType.WEB, "session-123"))
                    .thenReturn("jwt-access-token");
            when(jwtProperties.getWeb()).thenReturn(new JwtProperties.Web());

            // When
            AuthResponse response = authenticationService.login(loginRequest);

            // Then
            assertThat(response).isNotNull();
            assertThat(response.getAccessToken()).isEqualTo("jwt-access-token");
            assertThat(response.getRefreshToken()).isEqualTo("refresh-token-123");
            assertThat(response.getExpiresIn()).isEqualTo(3600L);
            assertThat(response.getUser()).isNotNull();
            assertThat(response.getUser().getEmail()).isEqualTo("<EMAIL>");
            assertThat(response.getUser().getFirstName()).isEqualTo("John");
            assertThat(response.getUser().getLastName()).isEqualTo("Doe");

            verify(userService).validateUserCredentials("<EMAIL>", "password123");
            verify(refreshTokenService).invalidateTokensForSessionType(testUser, SessionType.WEB);
            verify(refreshTokenService).generateRefreshToken(testUser, SessionType.WEB);
            verify(jwtService).generateAccessToken(testUser, SessionType.WEB, "session-123");
        }
    }

    @Nested
    @DisplayName("Login with Username Tests")
    class LoginWithUsernameTests {

        @Test
        @DisplayName("Should login successfully with username identifier")
        void shouldLoginSuccessfullyWithUsername() {
            // Given
            LoginRequest loginRequest = new LoginRequest();
            loginRequest.setIdentifier("jdoe");
            loginRequest.setPassword("password123");
            loginRequest.setSessionType(SessionType.WEB);

            when(userService.validateUserCredentials("jdoe", "password123"))
                    .thenReturn(testUser);
            when(refreshTokenService.hasValidTokenForSessionType(testUser, SessionType.WEB))
                    .thenReturn(false);
            when(refreshTokenService.generateRefreshToken(testUser, SessionType.WEB))
                    .thenReturn(testRefreshToken);
            when(jwtService.generateAccessToken(testUser, SessionType.WEB, "session-123"))
                    .thenReturn("jwt-access-token");
            when(jwtProperties.getWeb()).thenReturn(new JwtProperties.Web());

            // When
            AuthResponse response = authenticationService.login(loginRequest);

            // Then
            assertThat(response).isNotNull();
            assertThat(response.getAccessToken()).isEqualTo("jwt-access-token");
            assertThat(response.getRefreshToken()).isEqualTo("refresh-token-123");
            assertThat(response.getExpiresIn()).isEqualTo(3600L);
            assertThat(response.getUser()).isNotNull();
            assertThat(response.getUser().getEmail()).isEqualTo("<EMAIL>");
            assertThat(response.getUser().getFirstName()).isEqualTo("John");
            assertThat(response.getUser().getLastName()).isEqualTo("Doe");

            verify(userService).validateUserCredentials("jdoe", "password123");
            verify(refreshTokenService).invalidateTokensForSessionType(testUser, SessionType.WEB);
            verify(refreshTokenService).generateRefreshToken(testUser, SessionType.WEB);
            verify(jwtService).generateAccessToken(testUser, SessionType.WEB, "session-123");
        }

        @Test
        @DisplayName("Should login successfully with username in mobile session")
        void shouldLoginSuccessfullyWithUsernameInMobileSession() {
            // Given
            LoginRequest loginRequest = new LoginRequest();
            loginRequest.setIdentifier("jdoe");
            loginRequest.setPassword("password123");
            loginRequest.setSessionType(SessionType.MOBILE);
            loginRequest.setPushToken("push-token-456");
            loginRequest.setDeviceType(DeviceType.ANDROID);
            loginRequest.setDeviceId("device-789");

            testRefreshToken.setSessionType(SessionType.MOBILE);

            when(userService.validateUserCredentials("jdoe", "password123"))
                    .thenReturn(testUser);
            when(refreshTokenService.hasValidTokenForSessionType(testUser, SessionType.MOBILE))
                    .thenReturn(false);
            when(refreshTokenService.generateRefreshToken(testUser, SessionType.MOBILE))
                    .thenReturn(testRefreshToken);
            when(jwtService.generateAccessToken(testUser, SessionType.MOBILE, "session-123"))
                    .thenReturn("jwt-mobile-token");
            when(jwtProperties.getMobile()).thenReturn(new JwtProperties.Mobile());
            when(pushTokenService.registerPushToken(testUser, "push-token-456", DeviceType.ANDROID, "device-789"))
                    .thenReturn(new PushToken());

            // When
            AuthResponse response = authenticationService.login(loginRequest);

            // Then
            assertThat(response).isNotNull();
            assertThat(response.getAccessToken()).isEqualTo("jwt-mobile-token");
            assertThat(response.getRefreshToken()).isEqualTo("refresh-token-123");
            assertThat(response.getExpiresIn()).isEqualTo(7200L);
            assertThat(response.getUser()).isNotNull();
            assertThat(response.getUser().getFirstName()).isEqualTo("John");

            verify(userService).validateUserCredentials("jdoe", "password123");
            verify(pushTokenService).registerPushToken(testUser, "push-token-456", DeviceType.ANDROID, "device-789");
        }
    }

    @Nested
    @DisplayName("Login Error Handling Tests")
    class LoginErrorHandlingTests {

        @Test
        @DisplayName("Should throw exception for invalid username")
        void shouldThrowExceptionForInvalidUsername() {
            // Given
            LoginRequest loginRequest = new LoginRequest();
            loginRequest.setIdentifier("invaliduser");
            loginRequest.setPassword("password123");
            loginRequest.setSessionType(SessionType.WEB);

            when(userService.validateUserCredentials("invaliduser", "password123"))
                    .thenThrow(new UserNotFoundException("identifier", "invaliduser"));

            // When & Then
            assertThatThrownBy(() -> authenticationService.login(loginRequest))
                    .isInstanceOf(InvalidCredentialsException.class)
                    .hasMessageContaining("Invalid username/email or password");

            verify(userService).validateUserCredentials("invaliduser", "password123");
            verify(refreshTokenService, never()).generateRefreshToken(any(), any());
            verify(jwtService, never()).generateAccessToken(any(), any(), any());
        }

        @Test
        @DisplayName("Should throw exception for invalid email")
        void shouldThrowExceptionForInvalidEmail() {
            // Given
            LoginRequest loginRequest = new LoginRequest();
            loginRequest.setIdentifier("<EMAIL>");
            loginRequest.setPassword("password123");
            loginRequest.setSessionType(SessionType.WEB);

            when(userService.validateUserCredentials("<EMAIL>", "password123"))
                    .thenThrow(new UserNotFoundException("identifier", "<EMAIL>"));

            // When & Then
            assertThatThrownBy(() -> authenticationService.login(loginRequest))
                    .isInstanceOf(InvalidCredentialsException.class)
                    .hasMessageContaining("Invalid username/email or password");

            verify(userService).validateUserCredentials("<EMAIL>", "password123");
        }

        @Test
        @DisplayName("Should throw exception for deleted user")
        void shouldThrowExceptionForDeletedUser() {
            // Given
            LoginRequest loginRequest = new LoginRequest();
            loginRequest.setIdentifier("jdoe");
            loginRequest.setPassword("password123");
            loginRequest.setSessionType(SessionType.WEB);

            when(userService.validateUserCredentials("jdoe", "password123"))
                    .thenThrow(new UserNotFoundException("User account has been deleted"));

            // When & Then
            assertThatThrownBy(() -> authenticationService.login(loginRequest))
                    .isInstanceOf(InvalidCredentialsException.class)
                    .hasMessageContaining("Invalid username/email or password");

            verify(userService).validateUserCredentials("jdoe", "password123");
        }

        @Test
        @DisplayName("Should throw exception for inactive user")
        void shouldThrowExceptionForInactiveUser() {
            // Given
            LoginRequest loginRequest = new LoginRequest();
            loginRequest.setIdentifier("jdoe");
            loginRequest.setPassword("password123");
            loginRequest.setSessionType(SessionType.WEB);

            when(userService.validateUserCredentials("jdoe", "password123"))
                    .thenThrow(new UserNotFoundException("User account is inactive"));

            // When & Then
            assertThatThrownBy(() -> authenticationService.login(loginRequest))
                    .isInstanceOf(InvalidCredentialsException.class)
                    .hasMessageContaining("Invalid username/email or password");

            verify(userService).validateUserCredentials("jdoe", "password123");
        }
    }
}
