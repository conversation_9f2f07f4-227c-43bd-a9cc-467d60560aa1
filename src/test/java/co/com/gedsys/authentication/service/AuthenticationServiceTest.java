package co.com.gedsys.authentication.service;

import co.com.gedsys.authentication.config.JwtProperties;
import co.com.gedsys.authentication.dto.AuthResponse;
import co.com.gedsys.authentication.dto.LoginRequest;
import co.com.gedsys.authentication.dto.UserProfileResponse;
import co.com.gedsys.authentication.entity.*;
import co.com.gedsys.authentication.exception.InvalidCredentialsException;
import co.com.gedsys.authentication.exception.UserNotFoundException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for AuthenticationService orchestration.
 * Tests login, logout, token refresh, and service coordination.
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("Authentication Service Tests")
class AuthenticationServiceTest {

    @Mock
    private UserService userService;

    @Mock
    private JwtService jwtService;

    @Mock
    private RefreshTokenService refreshTokenService;

    @Mock
    private PushTokenService pushTokenService;

    @Mock
    private JwtProperties jwtProperties;

    @InjectMocks
    private AuthenticationService authenticationService;

    private User testUser;
    private LoginRequest webLoginRequest;
    private LoginRequest mobileLoginRequest;
    private RefreshToken testRefreshToken;

    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setId(1L);
        testUser.setEmail("<EMAIL>");
        testUser.setPassword("encodedPassword");
        testUser.setFirstName("Test");
        testUser.setLastName("User");
        testUser.setRole(Role.USER);
        testUser.setEnabled(true);
        testUser.setCreatedAt(LocalDateTime.now());
        testUser.setUpdatedAt(LocalDateTime.now());

        webLoginRequest = new LoginRequest();
        webLoginRequest.setIdentifier("<EMAIL>");
        webLoginRequest.setPassword("password123");
        webLoginRequest.setSessionType(SessionType.WEB);

        mobileLoginRequest = new LoginRequest();
        mobileLoginRequest.setIdentifier("<EMAIL>");
        mobileLoginRequest.setPassword("password123");
        mobileLoginRequest.setSessionType(SessionType.MOBILE);
        mobileLoginRequest.setPushToken("push-token-123");
        mobileLoginRequest.setDeviceType(DeviceType.ANDROID);
        mobileLoginRequest.setDeviceId("device-456");

        testRefreshToken = new RefreshToken();
        testRefreshToken.setId(1L);
        testRefreshToken.setToken("refresh-token-123");
        testRefreshToken.setUser(testUser);
        testRefreshToken.setSessionType(SessionType.WEB);
        testRefreshToken.setSessionId("test-session-123");
        testRefreshToken.setExpiryDate(LocalDateTime.now().plusDays(7));
        testRefreshToken.setCreatedAt(LocalDateTime.now());
        testRefreshToken.setUsed(false);

        // Mock JWT properties
        JwtProperties.Web webProperties = new JwtProperties.Web();
        webProperties.setAccessTokenExpiration(3600000L); // 1 hour
        JwtProperties.Mobile mobileProperties = new JwtProperties.Mobile();
        mobileProperties.setAccessTokenExpiration(7200000L); // 2 hours

        when(jwtProperties.getWeb()).thenReturn(webProperties);
        when(jwtProperties.getMobile()).thenReturn(mobileProperties);
    }

    @Nested
    @DisplayName("Login Tests")
    class LoginTests {

        @Test
        @DisplayName("Should login successfully with web session")
        void shouldLoginSuccessfullyWithWebSession() {
            // Given
            when(userService.validateUserCredentials("<EMAIL>", "password123"))
                    .thenReturn(testUser);
            when(jwtService.generateAccessToken(eq(testUser), eq(SessionType.WEB), anyString()))
                    .thenReturn("jwt-access-token");
            when(refreshTokenService.generateRefreshToken(testUser, SessionType.WEB))
                    .thenReturn(testRefreshToken);

            // When
            AuthResponse response = authenticationService.login(webLoginRequest);

            // Then
            assertThat(response).isNotNull();
            assertThat(response.getAccessToken()).isEqualTo("jwt-access-token");
            assertThat(response.getRefreshToken()).isEqualTo("refresh-token-123");
            assertThat(response.getTokenType()).isEqualTo("Bearer");
            assertThat(response.getExpiresIn()).isEqualTo(3600L); // 1 hour in seconds
            assertThat(response.getUser()).isNotNull();
            assertThat(response.getUser().getEmail()).isEqualTo("<EMAIL>");

            verify(userService).validateUserCredentials("<EMAIL>", "password123");
            verify(refreshTokenService).invalidateTokensForSessionType(testUser, SessionType.WEB);
            verify(jwtService).generateAccessToken(eq(testUser), eq(SessionType.WEB), anyString());
            verify(refreshTokenService).generateRefreshToken(testUser, SessionType.WEB);
            verify(pushTokenService, never()).registerPushToken(any(), any(), any(), any());
        }

        @Test
        @DisplayName("Should login successfully with mobile session and push token")
        void shouldLoginSuccessfullyWithMobileSessionAndPushToken() {
            // Given
            when(userService.validateUserCredentials("<EMAIL>", "password123"))
                    .thenReturn(testUser);
            when(jwtService.generateAccessToken(eq(testUser), eq(SessionType.MOBILE), anyString()))
                    .thenReturn("jwt-mobile-token");
            when(refreshTokenService.generateRefreshToken(testUser, SessionType.MOBILE))
                    .thenReturn(testRefreshToken);
            when(pushTokenService.registerPushToken(testUser, "push-token-123", DeviceType.ANDROID, "device-456"))
                    .thenReturn(new PushToken());

            // When
            AuthResponse response = authenticationService.login(mobileLoginRequest);

            // Then
            assertThat(response).isNotNull();
            assertThat(response.getAccessToken()).isEqualTo("jwt-mobile-token");
            assertThat(response.getExpiresIn()).isEqualTo(7200L); // 2 hours in seconds

            verify(userService).validateUserCredentials("<EMAIL>", "password123");
            verify(refreshTokenService).invalidateTokensForSessionType(testUser, SessionType.MOBILE);
            verify(pushTokenService).validateMobileDeviceType(DeviceType.ANDROID);
            verify(pushTokenService).registerPushToken(testUser, "push-token-123", DeviceType.ANDROID, "device-456");
            verify(jwtService).generateAccessToken(eq(testUser), eq(SessionType.MOBILE), anyString());
            verify(refreshTokenService).generateRefreshToken(testUser, SessionType.MOBILE);
        }

        @Test
        @DisplayName("Should login successfully with mobile session without push token")
        void shouldLoginSuccessfullyWithMobileSessionWithoutPushToken() {
            // Given
            LoginRequest mobileRequestWithoutPush = new LoginRequest();
            mobileRequestWithoutPush.setIdentifier("<EMAIL>");
            mobileRequestWithoutPush.setPassword("password123");
            mobileRequestWithoutPush.setSessionType(SessionType.MOBILE);
            // No push token set

            when(userService.validateUserCredentials("<EMAIL>", "password123"))
                    .thenReturn(testUser);
            when(jwtService.generateAccessToken(eq(testUser), eq(SessionType.MOBILE), anyString()))
                    .thenReturn("jwt-mobile-token");
            when(refreshTokenService.generateRefreshToken(testUser, SessionType.MOBILE))
                    .thenReturn(testRefreshToken);

            // When
            AuthResponse response = authenticationService.login(mobileRequestWithoutPush);

            // Then
            assertThat(response).isNotNull();
            assertThat(response.getAccessToken()).isEqualTo("jwt-mobile-token");

            verify(userService).validateUserCredentials("<EMAIL>", "password123");
            verify(pushTokenService, never()).registerPushToken(any(), any(), any(), any());
        }

        @Test
        @DisplayName("Should throw exception for invalid credentials")
        void shouldThrowExceptionForInvalidCredentials() {
            // Given
            when(userService.validateUserCredentials("<EMAIL>", "wrongPassword"))
                    .thenThrow(new UserNotFoundException("Invalid credentials"));

            LoginRequest invalidRequest = new LoginRequest();
            invalidRequest.setIdentifier("<EMAIL>");
            invalidRequest.setPassword("wrongPassword");
            invalidRequest.setSessionType(SessionType.WEB);

            // When & Then
            assertThatThrownBy(() -> authenticationService.login(invalidRequest))
                    .isInstanceOf(InvalidCredentialsException.class)
                    .hasMessageContaining("Invalid username/email or password");

            verify(userService).validateUserCredentials("<EMAIL>", "wrongPassword");
            verify(jwtService, never()).generateAccessToken(any(), any(), any());
            verify(refreshTokenService, never()).generateRefreshToken(any(), any());
        }

        @Test
        @DisplayName("Should handle push token registration failure gracefully")
        void shouldHandlePushTokenRegistrationFailureGracefully() {
            // Given
            when(userService.validateUserCredentials("<EMAIL>", "password123"))
                    .thenReturn(testUser);
            when(jwtService.generateAccessToken(eq(testUser), eq(SessionType.MOBILE), anyString()))
                    .thenReturn("jwt-mobile-token");
            when(refreshTokenService.generateRefreshToken(testUser, SessionType.MOBILE))
                    .thenReturn(testRefreshToken);
            when(pushTokenService.registerPushToken(any(), any(), any(), any()))
                    .thenThrow(new RuntimeException("Push token registration failed"));

            // When
            AuthResponse response = authenticationService.login(mobileLoginRequest);

            // Then
            assertThat(response).isNotNull();
            assertThat(response.getAccessToken()).isEqualTo("jwt-mobile-token");

            verify(pushTokenService).registerPushToken(testUser, "push-token-123", DeviceType.ANDROID, "device-456");
            // Login should still succeed even if push token registration fails
        }

        @Test
        @DisplayName("Should invalidate existing session tokens before login")
        void shouldInvalidateExistingSessionTokensBeforeLogin() {
            // Given
            when(userService.validateUserCredentials("<EMAIL>", "password123"))
                    .thenReturn(testUser);
            when(jwtService.generateAccessToken(eq(testUser), eq(SessionType.WEB), anyString()))
                    .thenReturn("jwt-access-token");
            when(refreshTokenService.generateRefreshToken(testUser, SessionType.WEB))
                    .thenReturn(testRefreshToken);

            // When
            authenticationService.login(webLoginRequest);

            // Then
            verify(refreshTokenService).invalidateTokensForSessionType(testUser, SessionType.WEB);
        }
    }

    @Nested
    @DisplayName("Token Refresh Tests")
    class TokenRefreshTests {

        @Test
        @DisplayName("Should refresh token successfully")
        void shouldRefreshTokenSuccessfully() {
            // Given
            RefreshToken newRefreshToken = new RefreshToken();
            newRefreshToken.setToken("new-refresh-token");
            newRefreshToken.setUser(testUser);
            newRefreshToken.setSessionType(SessionType.WEB);
            newRefreshToken.setSessionId("new-session-456");

            when(refreshTokenService.validateAndConsumeToken("refresh-token-123"))
                    .thenReturn(testRefreshToken);
            when(jwtService.generateAccessToken(eq(testUser), eq(SessionType.WEB), anyString()))
                    .thenReturn("new-jwt-token");
            when(refreshTokenService.generateRefreshToken(testUser, SessionType.WEB))
                    .thenReturn(newRefreshToken);

            // When
            AuthResponse response = authenticationService.refreshToken("refresh-token-123");

            // Then
            assertThat(response).isNotNull();
            assertThat(response.getAccessToken()).isEqualTo("new-jwt-token");
            assertThat(response.getRefreshToken()).isEqualTo("new-refresh-token");
            assertThat(response.getExpiresIn()).isEqualTo(3600L);
            assertThat(response.getUser().getEmail()).isEqualTo("<EMAIL>");

            verify(refreshTokenService).validateAndConsumeToken("refresh-token-123");
            verify(jwtService).generateAccessToken(eq(testUser), eq(SessionType.WEB), anyString());
            verify(refreshTokenService).generateRefreshToken(testUser, SessionType.WEB);
        }

        @Test
        @DisplayName("Should throw exception for invalid refresh token")
        void shouldThrowExceptionForInvalidRefreshToken() {
            // Given
            when(refreshTokenService.validateAndConsumeToken("invalid-refresh-token"))
                    .thenThrow(new IllegalArgumentException("Invalid refresh token"));

            // When & Then
            assertThatThrownBy(() -> authenticationService.refreshToken("invalid-refresh-token"))
                    .isInstanceOf(InvalidCredentialsException.class)
                    .hasMessageContaining("Invalid or expired refresh token");

            verify(refreshTokenService).validateAndConsumeToken("invalid-refresh-token");
            verify(jwtService, never()).generateAccessToken(any(), any(), any());
        }

        @Test
        @DisplayName("Should handle refresh token service exceptions")
        void shouldHandleRefreshTokenServiceExceptions() {
            // Given
            when(refreshTokenService.validateAndConsumeToken("refresh-token-123"))
                    .thenThrow(new RuntimeException("Database error"));

            // When & Then
            assertThatThrownBy(() -> authenticationService.refreshToken("refresh-token-123"))
                    .isInstanceOf(InvalidCredentialsException.class)
                    .hasMessageContaining("Token refresh failed");
        }
    }

    @Nested
    @DisplayName("Logout Tests")
    class LogoutTests {

        @Test
        @DisplayName("Should logout from web session successfully")
        void shouldLogoutFromWebSessionSuccessfully() {
            // When
            authenticationService.logout(testUser, SessionType.WEB);

            // Then
            verify(refreshTokenService).invalidateTokensForSessionType(testUser, SessionType.WEB);
            verify(pushTokenService, never()).removePushToken(any());
        }

        @Test
        @DisplayName("Should logout from mobile session and remove push token")
        void shouldLogoutFromMobileSessionAndRemovePushToken() {
            // Given
            when(pushTokenService.removePushToken(testUser)).thenReturn(true);

            // When
            authenticationService.logout(testUser, SessionType.MOBILE);

            // Then
            verify(refreshTokenService).invalidateTokensForSessionType(testUser, SessionType.MOBILE);
            verify(pushTokenService).removePushToken(testUser);
        }

        @Test
        @DisplayName("Should handle logout gracefully when push token removal fails")
        void shouldHandleLogoutGracefullyWhenPushTokenRemovalFails() {
            // Given
            when(pushTokenService.removePushToken(testUser))
                    .thenThrow(new RuntimeException("Push token removal failed"));

            // When
            authenticationService.logout(testUser, SessionType.MOBILE);

            // Then
            verify(refreshTokenService).invalidateTokensForSessionType(testUser, SessionType.MOBILE);
            verify(pushTokenService).removePushToken(testUser);
            // Should not throw exception
        }

        @Test
        @DisplayName("Should logout from all sessions successfully")
        void shouldLogoutFromAllSessionsSuccessfully() {
            // Given
            when(pushTokenService.removePushToken(testUser)).thenReturn(true);

            // When
            authenticationService.logoutFromAllSessions(testUser);

            // Then
            verify(refreshTokenService).invalidateAllUserTokens(testUser);
            verify(pushTokenService).removePushToken(testUser);
        }

        @Test
        @DisplayName("Should handle logout from all sessions gracefully on errors")
        void shouldHandleLogoutFromAllSessionsGracefullyOnErrors() {
            // Given
            doThrow(new RuntimeException("Database error"))
                    .when(refreshTokenService).invalidateAllUserTokens(testUser);

            // When
            authenticationService.logoutFromAllSessions(testUser);

            // Then
            verify(refreshTokenService).invalidateAllUserTokens(testUser);
            // Should not throw exception
        }
    }

    @Nested
    @DisplayName("Token Validation Tests")
    class TokenValidationTests {

        @Test
        @DisplayName("Should validate token successfully")
        void shouldValidateTokenSuccessfully() {
            // Given
            String validToken = "valid-jwt-token";
            when(jwtService.isTokenValid(validToken)).thenReturn(true);
            when(jwtService.extractUserId(validToken)).thenReturn(1L);
            when(userService.findById(1L)).thenReturn(testUser);
            when(jwtService.validateToken(validToken, testUser)).thenReturn(true);

            // When
            UserProfileResponse profile = authenticationService.validateToken(validToken);

            // Then
            assertThat(profile).isNotNull();
            assertThat(profile.getId()).isEqualTo(1L);
            assertThat(profile.getEmail()).isEqualTo("<EMAIL>");

            verify(jwtService).isTokenValid(validToken);
            verify(jwtService).extractUserId(validToken);
            verify(userService).findById(1L);
            verify(jwtService).validateToken(validToken, testUser);
        }

        @Test
        @DisplayName("Should throw exception for invalid token structure")
        void shouldThrowExceptionForInvalidTokenStructure() {
            // Given
            String invalidToken = "invalid-token";
            when(jwtService.isTokenValid(invalidToken)).thenReturn(false);

            // When & Then
            assertThatThrownBy(() -> authenticationService.validateToken(invalidToken))
                    .isInstanceOf(InvalidCredentialsException.class)
                    .hasMessageContaining("Invalid token");

            verify(jwtService).isTokenValid(invalidToken);
            verify(jwtService, never()).extractUserId(any());
        }

        @Test
        @DisplayName("Should throw exception when user not found")
        void shouldThrowExceptionWhenUserNotFound() {
            // Given
            String validToken = "valid-jwt-token";
            when(jwtService.isTokenValid(validToken)).thenReturn(true);
            when(jwtService.extractUserId(validToken)).thenReturn(999L);
            when(userService.findById(999L)).thenThrow(new UserNotFoundException(999L));

            // When & Then
            assertThatThrownBy(() -> authenticationService.validateToken(validToken))
                    .isInstanceOf(InvalidCredentialsException.class)
                    .hasMessageContaining("Invalid token");

            verify(userService).findById(999L);
        }

        @Test
        @DisplayName("Should throw exception when token validation fails")
        void shouldThrowExceptionWhenTokenValidationFails() {
            // Given
            String validToken = "valid-jwt-token";
            when(jwtService.isTokenValid(validToken)).thenReturn(true);
            when(jwtService.extractUserId(validToken)).thenReturn(1L);
            when(userService.findById(1L)).thenReturn(testUser);
            when(jwtService.validateToken(validToken, testUser)).thenReturn(false);

            // When & Then
            assertThatThrownBy(() -> authenticationService.validateToken(validToken))
                    .isInstanceOf(InvalidCredentialsException.class)
                    .hasMessageContaining("Invalid token");

            verify(jwtService).validateToken(validToken, testUser);
        }
    }

    @Nested
    @DisplayName("Session Management Tests")
    class SessionManagementTests {

        @Test
        @DisplayName("Should check if user has active session")
        void shouldCheckIfUserHasActiveSession() {
            // Given
            when(refreshTokenService.hasValidTokenForSessionType(testUser, SessionType.WEB))
                    .thenReturn(true);
            when(refreshTokenService.hasValidTokenForSessionType(testUser, SessionType.MOBILE))
                    .thenReturn(false);

            // When & Then
            assertThat(authenticationService.hasActiveSession(testUser, SessionType.WEB)).isTrue();
            assertThat(authenticationService.hasActiveSession(testUser, SessionType.MOBILE)).isFalse();

            verify(refreshTokenService).hasValidTokenForSessionType(testUser, SessionType.WEB);
            verify(refreshTokenService).hasValidTokenForSessionType(testUser, SessionType.MOBILE);
        }

        @Test
        @DisplayName("Should get active session count")
        void shouldGetActiveSessionCount() {
            // Given
            when(refreshTokenService.getValidTokenCountForUser(testUser)).thenReturn(2L);

            // When
            long count = authenticationService.getActiveSessionCount(testUser);

            // Then
            assertThat(count).isEqualTo(2L);
            verify(refreshTokenService).getValidTokenCountForUser(testUser);
        }
    }

    @Nested
    @DisplayName("Edge Cases and Error Handling")
    class EdgeCasesAndErrorHandlingTests {

        @Test
        @DisplayName("Should handle null login request gracefully")
        void shouldHandleNullLoginRequestGracefully() {
            // When & Then
            assertThatThrownBy(() -> authenticationService.login(null))
                    .isInstanceOf(NullPointerException.class);
        }

        @Test
        @DisplayName("Should handle null refresh token gracefully")
        void shouldHandleNullRefreshTokenGracefully() {
            // Given
            when(refreshTokenService.validateAndConsumeToken(null))
                    .thenThrow(new IllegalArgumentException("Token cannot be null"));

            // When & Then
            assertThatThrownBy(() -> authenticationService.refreshToken(null))
                    .isInstanceOf(InvalidCredentialsException.class);
        }

        @Test
        @DisplayName("Should handle empty refresh token gracefully")
        void shouldHandleEmptyRefreshTokenGracefully() {
            // Given
            when(refreshTokenService.validateAndConsumeToken(""))
                    .thenThrow(new IllegalArgumentException("Token cannot be empty"));

            // When & Then
            assertThatThrownBy(() -> authenticationService.refreshToken(""))
                    .isInstanceOf(InvalidCredentialsException.class);
        }

        @Test
        @DisplayName("Should handle JWT service exceptions during login")
        void shouldHandleJwtServiceExceptionsDuringLogin() {
            // Given
            when(userService.validateUserCredentials("<EMAIL>", "password123"))
                    .thenReturn(testUser);
            when(refreshTokenService.generateRefreshToken(testUser, SessionType.WEB))
                    .thenReturn(testRefreshToken);
            when(jwtService.generateAccessToken(eq(testUser), eq(SessionType.WEB), anyString()))
                    .thenThrow(new RuntimeException("JWT generation failed"));

            // When & Then
            assertThatThrownBy(() -> authenticationService.login(webLoginRequest))
                    .isInstanceOf(InvalidCredentialsException.class)
                    .hasMessageContaining("Authentication failed");
        }

        @Test
        @DisplayName("Should handle refresh token service exceptions during login")
        void shouldHandleRefreshTokenServiceExceptionsDuringLogin() {
            // Given
            when(userService.validateUserCredentials("<EMAIL>", "password123"))
                    .thenReturn(testUser);
            when(jwtService.generateAccessToken(eq(testUser), eq(SessionType.WEB), anyString()))
                    .thenReturn("jwt-token");
            when(refreshTokenService.generateRefreshToken(testUser, SessionType.WEB))
                    .thenThrow(new RuntimeException("Refresh token generation failed"));

            // When & Then
            assertThatThrownBy(() -> authenticationService.login(webLoginRequest))
                    .isInstanceOf(InvalidCredentialsException.class)
                    .hasMessageContaining("Authentication failed");
        }
    }
}