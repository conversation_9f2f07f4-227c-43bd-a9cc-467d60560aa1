package co.com.gedsys.authentication.service;

import co.com.gedsys.authentication.config.JwtProperties;
import co.com.gedsys.authentication.entity.Role;
import co.com.gedsys.authentication.entity.SessionType;
import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.entity.RefreshToken;
import co.com.gedsys.authentication.repository.RefreshTokenRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * Test class to verify sessionId functionality in JWT tokens and refresh tokens.
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class SessionIdValidationTest {

    @Mock
    private RefreshTokenRepository refreshTokenRepository;

    @Mock
    private JwtProperties jwtProperties;

    @InjectMocks
    private RefreshTokenService refreshTokenService;

    private JwtService jwtService;
    private User testUser;

    @BeforeEach
    void setUp() {
        // Setup mock JwtProperties
        when(jwtProperties.getSecret()).thenReturn("dGVzdC1zZWNyZXQta2V5LWZvci1qd3QtdG9rZW4tZ2VuZXJhdGlvbi1hbmQtdmFsaWRhdGlvbi10ZXN0aW5n");
        when(jwtProperties.getAccessTokenExpiration()).thenReturn(900000L);
        when(jwtProperties.getRefreshTokenExpiration()).thenReturn(604800000L);

        // Setup mock Web and Mobile properties
        JwtProperties.Web mockWeb = new JwtProperties.Web();
        mockWeb.setAccessTokenExpiration(900000L);
        when(jwtProperties.getWeb()).thenReturn(mockWeb);

        JwtProperties.Mobile mockMobile = new JwtProperties.Mobile();
        mockMobile.setAccessTokenExpiration(2592000000L);
        when(jwtProperties.getMobile()).thenReturn(mockMobile);

        // Initialize JwtService with mock properties
        jwtService = new JwtService(jwtProperties);

        // Create test user
        testUser = new User();
        testUser.setId(1L);
        testUser.setEmail("<EMAIL>");
        testUser.setFirstName("Test");
        testUser.setLastName("User");
        testUser.setRole(Role.USER);
        testUser.setEnabled(true);
    }

    @Test
    @DisplayName("Should include sessionId in access token")
    void shouldIncludeSessionIdInAccessToken() {
        // Given
        String sessionId = UUID.randomUUID().toString();
        
        // When
        String accessToken = jwtService.generateAccessToken(testUser, SessionType.WEB, sessionId);
        
        // Then
        assertThat(accessToken).isNotNull();
        String extractedSessionId = jwtService.extractSessionId(accessToken);
        assertThat(extractedSessionId).isEqualTo(sessionId);
    }

    @Test
    @DisplayName("Should extract sessionId from access token")
    void shouldExtractSessionIdFromAccessToken() {
        // Given
        String expectedSessionId = UUID.randomUUID().toString();
        String accessToken = jwtService.generateAccessToken(testUser, SessionType.WEB, expectedSessionId);
        
        // When
        String extractedSessionId = jwtService.extractSessionId(accessToken);
        
        // Then
        assertThat(extractedSessionId).isEqualTo(expectedSessionId);
    }

    @Test
    @DisplayName("Should validate active session correctly")
    void shouldValidateActiveSessionCorrectly() {
        // Given
        String sessionId = UUID.randomUUID().toString();

        when(refreshTokenRepository.existsByUserAndSessionTypeAndSessionIdAndUsedFalseAndExpiryDateAfter(
            eq(testUser), eq(SessionType.WEB), eq(sessionId), any(LocalDateTime.class)))
            .thenReturn(true);

        // When
        boolean isActive = refreshTokenService.isSessionActive(testUser, SessionType.WEB, sessionId);

        // Then
        assertThat(isActive).isTrue();
    }

    @Test
    @DisplayName("Should reject inactive session")
    void shouldRejectInactiveSession() {
        // Given
        String sessionId = UUID.randomUUID().toString();
        
        when(refreshTokenRepository.existsByUserAndSessionTypeAndSessionIdAndUsedFalseAndExpiryDateAfter(
            eq(testUser), eq(SessionType.WEB), eq(sessionId), any(LocalDateTime.class)))
            .thenReturn(false);
        
        // When
        boolean isActive = refreshTokenService.isSessionActive(testUser, SessionType.WEB, sessionId);
        
        // Then
        assertThat(isActive).isFalse();
    }

    @Test
    @DisplayName("Should reject null sessionId")
    void shouldRejectNullSessionId() {
        // When
        boolean isActive = refreshTokenService.isSessionActive(testUser, SessionType.WEB, null);
        
        // Then
        assertThat(isActive).isFalse();
    }

    @Test
    @DisplayName("Should generate unique sessionId for refresh tokens")
    void shouldGenerateUniqueSessionIdForRefreshTokens() {
        // Given
        RefreshToken mockToken1 = new RefreshToken();
        mockToken1.setSessionId(UUID.randomUUID().toString());
        
        RefreshToken mockToken2 = new RefreshToken();
        mockToken2.setSessionId(UUID.randomUUID().toString());
        
        when(refreshTokenRepository.save(any(RefreshToken.class)))
            .thenReturn(mockToken1)
            .thenReturn(mockToken2);
        
        // When
        RefreshToken token1 = refreshTokenService.generateRefreshToken(testUser, SessionType.WEB);
        RefreshToken token2 = refreshTokenService.generateRefreshToken(testUser, SessionType.MOBILE);
        
        // Then
        assertThat(token1.getSessionId()).isNotNull();
        assertThat(token2.getSessionId()).isNotNull();
        assertThat(token1.getSessionId()).isNotEqualTo(token2.getSessionId());
    }

}
