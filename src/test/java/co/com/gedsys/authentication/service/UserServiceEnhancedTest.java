package co.com.gedsys.authentication.service;

import co.com.gedsys.authentication.entity.Role;
import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.entity.UserStatus;
import co.com.gedsys.authentication.exception.EmailAlreadyExistsException;
import co.com.gedsys.authentication.exception.UserNotFoundException;
import co.com.gedsys.authentication.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Unit tests for enhanced UserService functionality.
 * Tests the new features: username generation, soft delete, and flexible login.
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("UserService Enhanced Functionality Tests")
class UserServiceEnhancedTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private PasswordEncoder passwordEncoder;

    @Mock
    private UsernameGenerator usernameGenerator;

    @Mock
    private RefreshTokenService refreshTokenService;

    private UserService userService;

    @BeforeEach
    void setUp() {
        userService = new UserService(userRepository, passwordEncoder, usernameGenerator, refreshTokenService);
    }

    @Nested
    @DisplayName("Create User Tests")
    class CreateUserTests {

        @Test
        @DisplayName("Should create user with auto-generated username")
        void shouldCreateUserWithAutoGeneratedUsername() {
            // Given
            User newUser = createTestUser(null, "<EMAIL>", "John", "Doe");
            User savedUser = createTestUser("jdoe", "<EMAIL>", "John", "Doe");
            savedUser.setId(1L);

            when(userRepository.existsByEmailIncludingDeleted("<EMAIL>")).thenReturn(false);
            when(usernameGenerator.generateUsername("John", "Doe", userRepository)).thenReturn("jdoe");
            when(passwordEncoder.encode("plainPassword")).thenReturn("encodedPassword");
            when(userRepository.save(any(User.class))).thenReturn(savedUser);

            // When
            User result = userService.createUser(newUser);

            // Then
            assertThat(result.getUsername()).isEqualTo("jdoe");
            assertThat(result.getStatus()).isEqualTo(UserStatus.ACTIVE);
            verify(usernameGenerator).generateUsername("John", "Doe", userRepository);
            verify(userRepository).save(any(User.class));
        }

        @Test
        @DisplayName("Should create user with provided username")
        void shouldCreateUserWithProvidedUsername() {
            // Given
            User newUser = createTestUser("customuser", "<EMAIL>", "John", "Doe");
            User savedUser = createTestUser("customuser", "<EMAIL>", "John", "Doe");
            savedUser.setId(1L);

            when(userRepository.existsByEmailIncludingDeleted("<EMAIL>")).thenReturn(false);
            when(userRepository.existsByUsernameIncludingDeleted("customuser")).thenReturn(false);
            when(passwordEncoder.encode("plainPassword")).thenReturn("encodedPassword");
            when(userRepository.save(any(User.class))).thenReturn(savedUser);

            // When
            User result = userService.createUser(newUser);

            // Then
            assertThat(result.getUsername()).isEqualTo("customuser");
            verify(usernameGenerator, never()).generateUsername(anyString(), anyString(), any());
            verify(userRepository).save(any(User.class));
        }

        @Test
        @DisplayName("Should throw exception when email exists including deleted users")
        void shouldThrowExceptionWhenEmailExistsIncludingDeleted() {
            // Given
            User newUser = createTestUser(null, "<EMAIL>", "John", "Doe");
            when(userRepository.existsByEmailIncludingDeleted("<EMAIL>")).thenReturn(true);

            // When & Then
            assertThatThrownBy(() -> userService.createUser(newUser))
                    .isInstanceOf(EmailAlreadyExistsException.class)
                    .hasMessageContaining("<EMAIL>");

            verify(userRepository, never()).save(any(User.class));
        }

        @Test
        @DisplayName("Should throw exception when provided username exists including deleted users")
        void shouldThrowExceptionWhenProvidedUsernameExistsIncludingDeleted() {
            // Given
            User newUser = createTestUser("existinguser", "<EMAIL>", "John", "Doe");
            when(userRepository.existsByEmailIncludingDeleted("<EMAIL>")).thenReturn(false);
            when(userRepository.existsByUsernameIncludingDeleted("existinguser")).thenReturn(true);

            // When & Then
            assertThatThrownBy(() -> userService.createUser(newUser))
                    .isInstanceOf(IllegalArgumentException.class)
                    .hasMessageContaining("Username already exists: existinguser");

            verify(userRepository, never()).save(any(User.class));
        }
    }

    @Nested
    @DisplayName("Soft Delete Tests")
    class SoftDeleteTests {

        @Test
        @DisplayName("Should soft delete user successfully and invalidate sessions")
        void shouldSoftDeleteUserSuccessfullyAndInvalidateSessions() {
            // Given
            User user = createTestUser("testuser", "<EMAIL>", "John", "Doe");
            user.setId(1L);
            user.setStatus(UserStatus.ACTIVE);
            user.setEnabled(true);

            when(userRepository.findById(1L)).thenReturn(Optional.of(user));
            when(userRepository.save(any(User.class))).thenReturn(user);
            when(refreshTokenService.getValidTokenCountForUser(user)).thenReturn(2L);

            // When
            userService.softDeleteUser(1L);

            // Then
            assertThat(user.getStatus()).isEqualTo(UserStatus.DELETED);
            assertThat(user.isEnabled()).isFalse();
            verify(userRepository).save(user);
            verify(refreshTokenService).getValidTokenCountForUser(user);
            verify(refreshTokenService).invalidateAllUserTokens(user);
        }

        @Test
        @DisplayName("Should throw exception when soft deleting already deleted user and not invalidate sessions")
        void shouldThrowExceptionWhenSoftDeletingAlreadyDeletedUserAndNotInvalidateSessions() {
            // Given
            User user = createTestUser("testuser", "<EMAIL>", "John", "Doe");
            user.setId(1L);
            user.setStatus(UserStatus.DELETED);

            when(userRepository.findById(1L)).thenReturn(Optional.of(user));

            // When & Then
            assertThatThrownBy(() -> userService.softDeleteUser(1L))
                    .isInstanceOf(IllegalStateException.class)
                    .hasMessageContaining("User is already deleted");

            verify(userRepository, never()).save(any(User.class));
            verify(refreshTokenService, never()).getValidTokenCountForUser(any(User.class));
            verify(refreshTokenService, never()).invalidateAllUserTokens(any(User.class));
        }

        @Test
        @DisplayName("Should throw exception when soft deleting non-existent user")
        void shouldThrowExceptionWhenSoftDeletingNonExistentUser() {
            // Given
            when(userRepository.findById(999L)).thenReturn(Optional.empty());

            // When & Then
            assertThatThrownBy(() -> userService.softDeleteUser(999L))
                    .isInstanceOf(UserNotFoundException.class);

            verify(userRepository, never()).save(any(User.class));
        }
    }

    @Nested
    @DisplayName("Validate User Credentials Tests")
    class ValidateUserCredentialsTests {

        @Test
        @DisplayName("Should validate credentials with username")
        void shouldValidateCredentialsWithUsername() {
            // Given
            User user = createTestUser("testuser", "<EMAIL>", "John", "Doe");
            user.setId(1L);
            user.setStatus(UserStatus.ACTIVE);
            user.setEnabled(true);
            user.setPassword("encodedPassword");

            when(userRepository.findByUsernameOrEmail("testuser")).thenReturn(Optional.of(user));
            when(passwordEncoder.matches("plainPassword", "encodedPassword")).thenReturn(true);

            // When
            User result = userService.validateUserCredentials("testuser", "plainPassword");

            // Then
            assertThat(result).isEqualTo(user);
            verify(userRepository).findByUsernameOrEmail("testuser");
        }

        @Test
        @DisplayName("Should validate credentials with email")
        void shouldValidateCredentialsWithEmail() {
            // Given
            User user = createTestUser("testuser", "<EMAIL>", "John", "Doe");
            user.setId(1L);
            user.setStatus(UserStatus.ACTIVE);
            user.setEnabled(true);
            user.setPassword("encodedPassword");

            when(userRepository.findByUsernameOrEmail("<EMAIL>")).thenReturn(Optional.of(user));
            when(passwordEncoder.matches("plainPassword", "encodedPassword")).thenReturn(true);

            // When
            User result = userService.validateUserCredentials("<EMAIL>", "plainPassword");

            // Then
            assertThat(result).isEqualTo(user);
            verify(userRepository).findByUsernameOrEmail("<EMAIL>");
        }

        @Test
        @DisplayName("Should throw exception when user is deleted")
        void shouldThrowExceptionWhenUserIsDeleted() {
            // Given
            User user = createTestUser("testuser", "<EMAIL>", "John", "Doe");
            user.setStatus(UserStatus.DELETED);

            when(userRepository.findByUsernameOrEmail("testuser")).thenReturn(Optional.of(user));

            // When & Then
            assertThatThrownBy(() -> userService.validateUserCredentials("testuser", "plainPassword"))
                    .isInstanceOf(UserNotFoundException.class)
                    .hasMessageContaining("User account has been deleted");
        }

        @Test
        @DisplayName("Should throw exception when user is inactive")
        void shouldThrowExceptionWhenUserIsInactive() {
            // Given
            User user = createTestUser("testuser", "<EMAIL>", "John", "Doe");
            user.setStatus(UserStatus.INACTIVE);
            user.setEnabled(true);

            when(userRepository.findByUsernameOrEmail("testuser")).thenReturn(Optional.of(user));

            // When & Then
            assertThatThrownBy(() -> userService.validateUserCredentials("testuser", "plainPassword"))
                    .isInstanceOf(UserNotFoundException.class)
                    .hasMessageContaining("User account is inactive");
        }
    }

    private User createTestUser(String username, String email, String firstName, String lastName) {
        User user = new User();
        user.setUsername(username);
        user.setEmail(email);
        user.setPassword("plainPassword");
        user.setFirstName(firstName);
        user.setLastName(lastName);
        user.setRole(Role.USER);
        user.setStatus(UserStatus.ACTIVE);
        user.setEnabled(true);
        return user;
    }
}
