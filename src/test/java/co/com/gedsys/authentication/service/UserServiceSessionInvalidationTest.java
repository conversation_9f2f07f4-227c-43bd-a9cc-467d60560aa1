package co.com.gedsys.authentication.service;

import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.entity.UserStatus;
import co.com.gedsys.authentication.exception.UserNotFoundException;
import co.com.gedsys.authentication.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.util.Optional;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Integration tests for UserService session invalidation functionality.
 * Tests the complete flow of soft deletion with session invalidation.
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("UserService Session Invalidation Integration Tests")
class UserServiceSessionInvalidationTest {

    @Mock
    private UserRepository userRepository;
    
    @Mock
    private PasswordEncoder passwordEncoder;
    
    @Mock
    private UsernameGenerator usernameGenerator;
    
    @Mock
    private RefreshTokenService refreshTokenService;
    
    private UserService userService;
    
    @BeforeEach
    void setUp() {
        userService = new UserService(userRepository, passwordEncoder, usernameGenerator, refreshTokenService);
    }

    @Test
    @DisplayName("Should complete full soft deletion flow with session invalidation")
    void shouldCompleteFullSoftDeletionFlowWithSessionInvalidation() {
        // Given
        User user = createTestUser();
        user.setId(1L);
        user.setStatus(UserStatus.ACTIVE);
        user.setEnabled(true);

        when(userRepository.findById(1L)).thenReturn(Optional.of(user));
        when(userRepository.save(any(User.class))).thenReturn(user);
        when(refreshTokenService.getValidTokenCountForUser(user)).thenReturn(3L);

        // When
        userService.softDeleteUser(1L);

        // Then - Verify user status changes
        assertThat(user.getStatus()).isEqualTo(UserStatus.DELETED);
        assertThat(user.isEnabled()).isFalse();

        // Verify repository interactions
        verify(userRepository).findById(1L);
        verify(userRepository).save(user);

        // Verify session invalidation flow
        verify(refreshTokenService).getValidTokenCountForUser(user);
        verify(refreshTokenService).invalidateAllUserTokens(user);

        // Verify order of operations: user saved before session invalidation
        var inOrder = inOrder(userRepository, refreshTokenService);
        inOrder.verify(userRepository).save(user);
        inOrder.verify(refreshTokenService).invalidateAllUserTokens(user);
    }

    @Test
    @DisplayName("Should handle soft deletion when user has no active sessions")
    void shouldHandleSoftDeletionWhenUserHasNoActiveSessions() {
        // Given
        User user = createTestUser();
        user.setId(2L);
        user.setStatus(UserStatus.ACTIVE);
        user.setEnabled(true);

        when(userRepository.findById(2L)).thenReturn(Optional.of(user));
        when(userRepository.save(any(User.class))).thenReturn(user);
        when(refreshTokenService.getValidTokenCountForUser(user)).thenReturn(0L);

        // When
        userService.softDeleteUser(2L);

        // Then
        assertThat(user.getStatus()).isEqualTo(UserStatus.DELETED);
        assertThat(user.isEnabled()).isFalse();
        
        verify(refreshTokenService).getValidTokenCountForUser(user);
        verify(refreshTokenService).invalidateAllUserTokens(user);
    }

    @Test
    @DisplayName("Should not invalidate sessions when user not found")
    void shouldNotInvalidateSessionsWhenUserNotFound() {
        // Given
        when(userRepository.findById(999L)).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> userService.softDeleteUser(999L))
                .isInstanceOf(UserNotFoundException.class);

        verify(refreshTokenService, never()).getValidTokenCountForUser(any(User.class));
        verify(refreshTokenService, never()).invalidateAllUserTokens(any(User.class));
        verify(userRepository, never()).save(any(User.class));
    }

    @Test
    @DisplayName("Should handle RefreshTokenService exceptions gracefully")
    void shouldHandleRefreshTokenServiceExceptionsGracefully() {
        // Given
        User user = createTestUser();
        user.setId(3L);
        user.setStatus(UserStatus.ACTIVE);
        user.setEnabled(true);

        when(userRepository.findById(3L)).thenReturn(Optional.of(user));
        when(userRepository.save(any(User.class))).thenReturn(user);
        when(refreshTokenService.getValidTokenCountForUser(user)).thenReturn(2L);
        doThrow(new RuntimeException("Token service error")).when(refreshTokenService).invalidateAllUserTokens(user);

        // When & Then - Should still complete user deletion even if token invalidation fails
        assertThatThrownBy(() -> userService.softDeleteUser(3L))
                .isInstanceOf(RuntimeException.class)
                .hasMessageContaining("Token service error");

        // Verify user was still saved before the exception
        verify(userRepository).save(user);
        assertThat(user.getStatus()).isEqualTo(UserStatus.DELETED);
        assertThat(user.isEnabled()).isFalse();
    }

    private User createTestUser() {
        User user = new User();
        user.setEmail("<EMAIL>");
        user.setUsername("testuser");
        user.setFirstName("Test");
        user.setLastName("User");
        user.setPassword("encodedPassword");
        return user;
    }
}
