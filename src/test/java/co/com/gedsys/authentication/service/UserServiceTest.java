package co.com.gedsys.authentication.service;

import co.com.gedsys.authentication.dto.PagedResponse;
import co.com.gedsys.authentication.dto.UserProfileResponse;
import co.com.gedsys.authentication.entity.Role;
import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.exception.EmailAlreadyExistsException;
import co.com.gedsys.authentication.exception.UserNotFoundException;
import co.com.gedsys.authentication.exception.UsernameAlreadyExistsException;
import co.com.gedsys.authentication.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for UserService CRUD operations.
 * Tests user management, password handling, and validation logic.
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("User Service Tests")
class UserServiceTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private PasswordEncoder passwordEncoder;

    @Mock
    private UsernameGenerator usernameGenerator;

    @Mock
    private RefreshTokenService refreshTokenService;

    private UserService userService;

    private User testUser;
    private User adminUser;

    @BeforeEach
    void setUp() {
        userService = new UserService(userRepository, passwordEncoder, usernameGenerator, refreshTokenService);

        testUser = new User();
        testUser.setId(1L);
        testUser.setEmail("<EMAIL>");
        testUser.setPassword("password123");
        testUser.setFirstName("Test");
        testUser.setLastName("User");
        testUser.setRole(Role.USER);
        testUser.setEnabled(true);
        testUser.setCreatedAt(LocalDateTime.now());
        testUser.setUpdatedAt(LocalDateTime.now());

        adminUser = new User();
        adminUser.setId(2L);
        adminUser.setEmail("<EMAIL>");
        adminUser.setPassword("adminpass123");
        adminUser.setFirstName("Admin");
        adminUser.setLastName("User");
        adminUser.setRole(Role.ADMIN);
        adminUser.setEnabled(true);
        adminUser.setCreatedAt(LocalDateTime.now());
        adminUser.setUpdatedAt(LocalDateTime.now());
    }

    @Nested
    @DisplayName("User Creation Tests")
    class UserCreationTests {

        @Test
        @DisplayName("Should create user successfully with encoded password")
        void shouldCreateUserSuccessfullyWithEncodedPassword() {
            // Given
            User newUser = new User();
            newUser.setEmail("<EMAIL>");
            newUser.setPassword("plainPassword");
            newUser.setFirstName("New");
            newUser.setLastName("User");

            when(userRepository.existsByEmailIncludingDeleted("<EMAIL>")).thenReturn(false);
            when(usernameGenerator.generateUsername("New", "User", userRepository)).thenReturn("nuser");
            when(passwordEncoder.encode("plainPassword")).thenReturn("encodedPassword");
            when(userRepository.save(any(User.class))).thenReturn(testUser);

            // When
            User createdUser = userService.createUser(newUser);

            // Then
            assertThat(createdUser).isNotNull();
            verify(userRepository).existsByEmailIncludingDeleted("<EMAIL>");
            verify(passwordEncoder).encode("plainPassword");
            verify(userRepository).save(argThat(user -> 
                user.getPassword().equals("encodedPassword") && 
                user.getRole() == Role.USER
            ));
        }

        @Test
        @DisplayName("Should set default role when creating user without role")
        void shouldSetDefaultRoleWhenCreatingUserWithoutRole() {
            // Given
            User newUser = new User();
            newUser.setEmail("<EMAIL>");
            newUser.setPassword("plainPassword");
            newUser.setRole(null);

            when(userRepository.existsByEmailIncludingDeleted("<EMAIL>")).thenReturn(false);
            when(usernameGenerator.generateUsername(null, null, userRepository)).thenReturn("user");
            when(passwordEncoder.encode("plainPassword")).thenReturn("encodedPassword");
            when(userRepository.save(any(User.class))).thenReturn(testUser);

            // When
            userService.createUser(newUser);

            // Then
            verify(userRepository).save(argThat(user -> user.getRole() == Role.USER));
        }

        @Test
        @DisplayName("Should throw exception when creating user with existing email")
        void shouldThrowExceptionWhenCreatingUserWithExistingEmail() {
            // Given
            User newUser = new User();
            newUser.setEmail("<EMAIL>");
            newUser.setPassword("plainPassword");

            when(userRepository.existsByEmailIncludingDeleted("<EMAIL>")).thenReturn(true);

            // When & Then
            assertThatThrownBy(() -> userService.createUser(newUser))
                    .isInstanceOf(EmailAlreadyExistsException.class)
                    .hasMessageContaining("<EMAIL>");

            verify(userRepository).existsByEmailIncludingDeleted("<EMAIL>");
            verify(passwordEncoder, never()).encode(anyString());
            verify(userRepository, never()).save(any(User.class));
        }

        @Test
        @DisplayName("Should throw exception when creating user with existing username")
        void shouldThrowExceptionWhenCreatingUserWithExistingUsername() {
            // Given
            User newUser = new User();
            newUser.setEmail("<EMAIL>");
            newUser.setUsername("existinguser");
            newUser.setPassword("plainPassword");

            when(userRepository.existsByEmailIncludingDeleted("<EMAIL>")).thenReturn(false);
            when(userRepository.existsByUsernameIncludingDeleted("existinguser")).thenReturn(true);

            // When & Then
            assertThatThrownBy(() -> userService.createUser(newUser))
                    .isInstanceOf(UsernameAlreadyExistsException.class)
                    .hasMessageContaining("existinguser");

            verify(userRepository).existsByEmailIncludingDeleted("<EMAIL>");
            verify(userRepository).existsByUsernameIncludingDeleted("existinguser");
            verify(passwordEncoder, never()).encode(anyString());
            verify(userRepository, never()).save(any(User.class));
        }

        @Test
        @DisplayName("Should preserve specified role when creating user")
        void shouldPreserveSpecifiedRoleWhenCreatingUser() {
            // Given
            User newUser = new User();
            newUser.setEmail("<EMAIL>");
            newUser.setPassword("plainPassword");
            newUser.setRole(Role.ADMIN);

            when(userRepository.existsByEmailIncludingDeleted("<EMAIL>")).thenReturn(false);
            when(usernameGenerator.generateUsername(null, null, userRepository)).thenReturn("admin");
            when(passwordEncoder.encode("plainPassword")).thenReturn("encodedPassword");
            when(userRepository.save(any(User.class))).thenReturn(adminUser);

            // When
            userService.createUser(newUser);

            // Then
            verify(userRepository).save(argThat(user -> user.getRole() == Role.ADMIN));
        }
    }

    @Nested
    @DisplayName("User Update Tests")
    class UserUpdateTests {

        @Test
        @DisplayName("Should update user successfully without password change")
        void shouldUpdateUserSuccessfullyWithoutPasswordChange() {
            // Given
            User updatedUser = new User();
            updatedUser.setEmail("<EMAIL>");
            updatedUser.setFirstName("Updated");
            updatedUser.setLastName("Name");
            updatedUser.setRole(Role.ADMIN);
            updatedUser.setEnabled(false);

            when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
            when(userRepository.existsByEmailIncludingDeleted("<EMAIL>")).thenReturn(false);
            when(userRepository.save(any(User.class))).thenReturn(testUser);

            // When
            User result = userService.updateUser(1L, updatedUser);

            // Then
            assertThat(result).isNotNull();
            verify(userRepository).findById(1L);
            verify(userRepository).existsByEmailIncludingDeleted("<EMAIL>");
            verify(userRepository).save(argThat(user -> 
                user.getEmail().equals("<EMAIL>") &&
                user.getFirstName().equals("Updated") &&
                user.getLastName().equals("Name") &&
                user.getRole() == Role.ADMIN &&
                !user.isEnabled()
            ));
            verify(passwordEncoder, never()).encode(anyString());
        }

        @Test
        @DisplayName("Should update user with password change")
        void shouldUpdateUserWithPasswordChange() {
            // Given
            User updatedUser = new User();
            updatedUser.setEmail("<EMAIL>");
            updatedUser.setPassword("newPassword");
            updatedUser.setFirstName("Updated");
            updatedUser.setLastName("Name");
            updatedUser.setRole(Role.USER);
            updatedUser.setEnabled(true);

            when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
            when(passwordEncoder.encode("newPassword")).thenReturn("encodedNewPassword");
            when(userRepository.save(any(User.class))).thenReturn(testUser);

            // When
            userService.updateUser(1L, updatedUser);

            // Then
            verify(passwordEncoder).encode("newPassword");
            verify(userRepository).save(argThat(user -> 
                user.getPassword().equals("encodedNewPassword")
            ));
        }

        @Test
        @DisplayName("Should throw exception when updating user with existing email")
        void shouldThrowExceptionWhenUpdatingUserWithExistingEmail() {
            // Given
            User updatedUser = new User();
            updatedUser.setEmail("<EMAIL>");

            when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
            when(userRepository.existsByEmailIncludingDeleted("<EMAIL>")).thenReturn(true);

            // When & Then
            assertThatThrownBy(() -> userService.updateUser(1L, updatedUser))
                    .isInstanceOf(EmailAlreadyExistsException.class)
                    .hasMessageContaining("<EMAIL>");

            verify(userRepository, never()).save(any(User.class));
        }

        @Test
        @DisplayName("Should throw exception when updating user with existing username")
        void shouldThrowExceptionWhenUpdatingUserWithExistingUsername() {
            // Given
            User updatedUser = new User();
            updatedUser.setUsername("existinguser");

            when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
            when(userRepository.existsByUsernameIncludingDeleted("existinguser")).thenReturn(true);

            // When & Then
            assertThatThrownBy(() -> userService.updateUser(1L, updatedUser))
                    .isInstanceOf(UsernameAlreadyExistsException.class)
                    .hasMessageContaining("existinguser");

            verify(userRepository, never()).save(any(User.class));
        }

        @Test
        @DisplayName("Should throw exception when updating non-existent user")
        void shouldThrowExceptionWhenUpdatingNonExistentUser() {
            // Given
            User updatedUser = new User();
            updatedUser.setEmail("<EMAIL>");

            when(userRepository.findById(999L)).thenReturn(Optional.empty());

            // When & Then
            assertThatThrownBy(() -> userService.updateUser(999L, updatedUser))
                    .isInstanceOf(UserNotFoundException.class);

            verify(userRepository, never()).save(any(User.class));
        }

        @Test
        @DisplayName("Should allow same email when updating user")
        void shouldAllowSameEmailWhenUpdatingUser() {
            // Given
            User updatedUser = new User();
            updatedUser.setEmail("<EMAIL>"); // Same email as testUser
            updatedUser.setFirstName("Updated");

            when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
            when(userRepository.save(any(User.class))).thenReturn(testUser);

            // When
            userService.updateUser(1L, updatedUser);

            // Then
            verify(userRepository, never()).existsByEmailIncludingDeleted(anyString());
            verify(userRepository).save(any(User.class));
        }
    }

    @Nested
    @DisplayName("User Deletion Tests")
    class UserDeletionTests {

        @Test
        @DisplayName("Should delete user successfully")
        void shouldDeleteUserSuccessfully() {
            // Given
            when(userRepository.existsById(1L)).thenReturn(true);

            // When
            userService.deleteUser(1L);

            // Then
            verify(userRepository).existsById(1L);
            verify(userRepository).deleteById(1L);
        }

        @Test
        @DisplayName("Should throw exception when deleting non-existent user")
        void shouldThrowExceptionWhenDeletingNonExistentUser() {
            // Given
            when(userRepository.existsById(999L)).thenReturn(false);

            // When & Then
            assertThatThrownBy(() -> userService.deleteUser(999L))
                    .isInstanceOf(UserNotFoundException.class);

            verify(userRepository, never()).deleteById(anyLong());
        }
    }

    @Nested
    @DisplayName("User Retrieval Tests")
    class UserRetrievalTests {

        @Test
        @DisplayName("Should find user by ID successfully")
        void shouldFindUserByIdSuccessfully() {
            // Given
            when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));

            // When
            User foundUser = userService.findById(1L);

            // Then
            assertThat(foundUser).isEqualTo(testUser);
            verify(userRepository).findById(1L);
        }

        @Test
        @DisplayName("Should throw exception when user not found by ID")
        void shouldThrowExceptionWhenUserNotFoundById() {
            // Given
            when(userRepository.findById(999L)).thenReturn(Optional.empty());

            // When & Then
            assertThatThrownBy(() -> userService.findById(999L))
                    .isInstanceOf(UserNotFoundException.class);
        }

        @Test
        @DisplayName("Should find user by email successfully")
        void shouldFindUserByEmailSuccessfully() {
            // Given
            when(userRepository.findByEmail("<EMAIL>")).thenReturn(Optional.of(testUser));

            // When
            Optional<User> foundUser = userService.findByEmail("<EMAIL>");

            // Then
            assertThat(foundUser).isPresent().contains(testUser);
            verify(userRepository).findByEmail("<EMAIL>");
        }

        @Test
        @DisplayName("Should return empty when user not found by email")
        void shouldReturnEmptyWhenUserNotFoundByEmail() {
            // Given
            when(userRepository.findByEmail("<EMAIL>")).thenReturn(Optional.empty());

            // When
            Optional<User> foundUser = userService.findByEmail("<EMAIL>");

            // Then
            assertThat(foundUser).isEmpty();
        }

        @Test
        @DisplayName("Should get user profile successfully")
        void shouldGetUserProfileSuccessfully() {
            // Given
            when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));

            // When
            UserProfileResponse profile = userService.getUserProfile(1L);

            // Then
            assertThat(profile).isNotNull();
            assertThat(profile.getId()).isEqualTo(testUser.getId());
            assertThat(profile.getEmail()).isEqualTo(testUser.getEmail());
            assertThat(profile.getFirstName()).isEqualTo(testUser.getFirstName());
            assertThat(profile.getLastName()).isEqualTo(testUser.getLastName());
            assertThat(profile.getRole()).isEqualTo(testUser.getRole());
        }
    }

    @Nested
    @DisplayName("User Listing Tests")
    class UserListingTests {

        @Test
        @DisplayName("Should get paginated users with filters")
        void shouldGetPaginatedUsersWithFilters() {
            // Given
            List<User> users = Arrays.asList(testUser, adminUser);
            Page<User> userPage = new PageImpl<>(users, PageRequest.of(0, 10), 2);
            Pageable pageable = PageRequest.of(0, 10);

            when(userRepository.findUsersWithFilters("test", true, Role.USER, pageable))
                    .thenReturn(userPage);

            // When
            PagedResponse<UserProfileResponse> result = userService.getUsers("test", true, Role.USER, pageable);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getContent()).hasSize(2);
            assertThat(result.getTotalElements()).isEqualTo(2);
            assertThat(result.getTotalPages()).isEqualTo(1);
            verify(userRepository).findUsersWithFilters("test", true, Role.USER, pageable);
        }

        @Test
        @DisplayName("Should get all users with pagination")
        void shouldGetAllUsersWithPagination() {
            // Given
            List<User> users = Arrays.asList(testUser, adminUser);
            Page<User> userPage = new PageImpl<>(users, PageRequest.of(0, 10), 2);
            Pageable pageable = PageRequest.of(0, 10);

            when(userRepository.findAll(pageable)).thenReturn(userPage);

            // When
            PagedResponse<UserProfileResponse> result = userService.getAllUsers(pageable);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getContent()).hasSize(2);
            assertThat(result.getTotalElements()).isEqualTo(2);
            verify(userRepository).findAll(pageable);
        }
    }

    @Nested
    @DisplayName("Credential Validation Tests")
    class CredentialValidationTests {

        @Test
        @DisplayName("Should validate user credentials successfully")
        void shouldValidateUserCredentialsSuccessfully() {
            // Given
            when(userRepository.findByUsernameOrEmail("<EMAIL>")).thenReturn(Optional.of(testUser));
            when(passwordEncoder.matches("password123", testUser.getPassword())).thenReturn(true);

            // When
            User validatedUser = userService.validateUserCredentials("<EMAIL>", "password123");

            // Then
            assertThat(validatedUser).isEqualTo(testUser);
            verify(userRepository).findByUsernameOrEmail("<EMAIL>");
            verify(passwordEncoder).matches("password123", testUser.getPassword());
        }

        @Test
        @DisplayName("Should throw exception for non-existent user")
        void shouldThrowExceptionForNonExistentUser() {
            // Given
            when(userRepository.findByUsernameOrEmail("<EMAIL>")).thenReturn(Optional.empty());

            // When & Then
            assertThatThrownBy(() -> userService.validateUserCredentials("<EMAIL>", "password"))
                    .isInstanceOf(UserNotFoundException.class);

            verify(passwordEncoder, never()).matches(anyString(), anyString());
        }

        @Test
        @DisplayName("Should throw exception for disabled user")
        void shouldThrowExceptionForDisabledUser() {
            // Given
            testUser.setEnabled(false);
            when(userRepository.findByUsernameOrEmail("<EMAIL>")).thenReturn(Optional.of(testUser));

            // When & Then
            assertThatThrownBy(() -> userService.validateUserCredentials("<EMAIL>", "password123"))
                    .isInstanceOf(UserNotFoundException.class)
                    .hasMessageContaining("disabled");

            verify(passwordEncoder, never()).matches(anyString(), anyString());
        }

        @Test
        @DisplayName("Should throw exception for invalid password")
        void shouldThrowExceptionForInvalidPassword() {
            // Given
            when(userRepository.findByUsernameOrEmail("<EMAIL>")).thenReturn(Optional.of(testUser));
            when(passwordEncoder.matches("wrongPassword", testUser.getPassword())).thenReturn(false);

            // When & Then
            assertThatThrownBy(() -> userService.validateUserCredentials("<EMAIL>", "wrongPassword"))
                    .isInstanceOf(UserNotFoundException.class)
                    .hasMessageContaining("Invalid credentials");

            verify(passwordEncoder).matches("wrongPassword", testUser.getPassword());
        }
    }

    @Nested
    @DisplayName("Password Change Tests")
    class PasswordChangeTests {

        @Test
        @DisplayName("Should change password successfully")
        void shouldChangePasswordSuccessfully() {
            // Given
            String currentPassword = "currentPassword";
            String newPassword = "newPassword";
            String userStoredPassword = testUser.getPassword(); // "password123"

            when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
            when(passwordEncoder.matches(currentPassword, userStoredPassword)).thenReturn(true);
            when(passwordEncoder.matches(newPassword, userStoredPassword)).thenReturn(false);
            when(passwordEncoder.encode(newPassword)).thenReturn("encodedNewPassword");
            when(userRepository.save(any(User.class))).thenReturn(testUser);

            // When
            userService.changePassword(1L, currentPassword, newPassword);

            // Then
            verify(userRepository).findById(1L);
            verify(passwordEncoder).matches(currentPassword, userStoredPassword);
            verify(passwordEncoder).matches(newPassword, userStoredPassword);
            verify(passwordEncoder).encode(newPassword);
            verify(userRepository).save(testUser);
        }

        @Test
        @DisplayName("Should throw exception for incorrect current password")
        void shouldThrowExceptionForIncorrectCurrentPassword() {
            // Given
            when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
            when(passwordEncoder.matches("wrongCurrentPassword", testUser.getPassword())).thenReturn(false);

            // When & Then
            assertThatThrownBy(() -> userService.changePassword(1L, "wrongCurrentPassword", "newPassword"))
                    .isInstanceOf(UserNotFoundException.class)
                    .hasMessageContaining("Current password is incorrect");

            verify(userRepository, never()).save(any(User.class));
        }

        @Test
        @DisplayName("Should throw exception when new password is same as current")
        void shouldThrowExceptionWhenNewPasswordIsSameAsCurrent() {
            // Given
            when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
            when(passwordEncoder.matches("currentPassword", testUser.getPassword())).thenReturn(true);
            when(passwordEncoder.matches("currentPassword", testUser.getPassword())).thenReturn(true);

            // When & Then
            assertThatThrownBy(() -> userService.changePassword(1L, "currentPassword", "currentPassword"))
                    .isInstanceOf(IllegalArgumentException.class)
                    .hasMessageContaining("New password must be different");

            verify(userRepository, never()).save(any(User.class));
        }

        @Test
        @DisplayName("Should throw exception when user not found for password change")
        void shouldThrowExceptionWhenUserNotFoundForPasswordChange() {
            // Given
            when(userRepository.findById(999L)).thenReturn(Optional.empty());

            // When & Then
            assertThatThrownBy(() -> userService.changePassword(999L, "currentPassword", "newPassword"))
                    .isInstanceOf(UserNotFoundException.class);

            verify(passwordEncoder, never()).matches(anyString(), anyString());
            verify(userRepository, never()).save(any(User.class));
        }
    }

    @Nested
    @DisplayName("Utility Methods Tests")
    class UtilityMethodsTests {

        @Test
        @DisplayName("Should check if email exists")
        void shouldCheckIfEmailExists() {
            // Given
            when(userRepository.existsByEmail("<EMAIL>")).thenReturn(true);
            when(userRepository.existsByEmail("<EMAIL>")).thenReturn(false);

            // When & Then
            assertThat(userService.emailExists("<EMAIL>")).isTrue();
            assertThat(userService.emailExists("<EMAIL>")).isFalse();
        }

        @Test
        @DisplayName("Should get user count by enabled status")
        void shouldGetUserCountByEnabledStatus() {
            // Given
            when(userRepository.countByEnabled(true)).thenReturn(5L);
            when(userRepository.countByEnabled(false)).thenReturn(2L);

            // When & Then
            assertThat(userService.getUserCountByEnabled(true)).isEqualTo(5L);
            assertThat(userService.getUserCountByEnabled(false)).isEqualTo(2L);
        }

        @Test
        @DisplayName("Should get user count by role")
        void shouldGetUserCountByRole() {
            // Given
            when(userRepository.countByRole(Role.USER)).thenReturn(10L);
            when(userRepository.countByRole(Role.ADMIN)).thenReturn(3L);

            // When & Then
            assertThat(userService.getUserCountByRole(Role.USER)).isEqualTo(10L);
            assertThat(userService.getUserCountByRole(Role.ADMIN)).isEqualTo(3L);
        }
    }
}