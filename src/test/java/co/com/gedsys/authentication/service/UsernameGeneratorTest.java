package co.com.gedsys.authentication.service;

import co.com.gedsys.authentication.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;

/**
 * Unit tests for UsernameGenerator component.
 * Tests the username generation algorithm and edge cases.
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("UsernameGenerator Tests")
class UsernameGeneratorTest {

    @Mock
    private UserRepository userRepository;

    private UsernameGenerator usernameGenerator;

    @BeforeEach
    void setUp() {
        usernameGenerator = new UsernameGenerator();
    }

    @Test
    @DisplayName("Should generate basic username from first initial and last name")
    void shouldGenerateBasicUsername() {
        // Given
        String firstName = "Jose";
        String lastName = "Marin";
        when(userRepository.existsByUsernameIncludingDeleted("jmarin")).thenReturn(false);

        // When
        String result = usernameGenerator.generateUsername(firstName, lastName, userRepository);

        // Then
        assertThat(result).isEqualTo("jmarin");
    }

    @Test
    @DisplayName("Should handle names with spaces and special characters")
    void shouldHandleNamesWithSpacesAndSpecialCharacters() {
        // Given
        String firstName = "José Luis";
        String lastName = "Marín Arango";
        when(userRepository.existsByUsernameIncludingDeleted("jmarinarango")).thenReturn(false);

        // When
        String result = usernameGenerator.generateUsername(firstName, lastName, userRepository);

        // Then
        assertThat(result).isEqualTo("jmarinarango");
    }

    @Test
    @DisplayName("Should remove diacritics and special characters")
    void shouldRemoveDiacriticsAndSpecialCharacters() {
        // Given
        String firstName = "María José";
        String lastName = "González-Pérez";
        when(userRepository.existsByUsernameIncludingDeleted("mgonzalezperez")).thenReturn(false);

        // When
        String result = usernameGenerator.generateUsername(firstName, lastName, userRepository);

        // Then
        assertThat(result).isEqualTo("mgonzalezperez");
    }

    @Test
    @DisplayName("Should add incremental number when username exists")
    void shouldAddIncrementalNumberWhenUsernameExists() {
        // Given
        String firstName = "Jose";
        String lastName = "Marin";
        when(userRepository.existsByUsernameIncludingDeleted("jmarin")).thenReturn(true);
        when(userRepository.existsByUsernameIncludingDeleted("jmarin2")).thenReturn(false);

        // When
        String result = usernameGenerator.generateUsername(firstName, lastName, userRepository);

        // Then
        assertThat(result).isEqualTo("jmarin2");
    }

    @Test
    @DisplayName("Should continue incrementing until finding available username")
    void shouldContinueIncrementingUntilFindingAvailableUsername() {
        // Given
        String firstName = "Jose";
        String lastName = "Marin";
        when(userRepository.existsByUsernameIncludingDeleted("jmarin")).thenReturn(true);
        when(userRepository.existsByUsernameIncludingDeleted("jmarin2")).thenReturn(true);
        when(userRepository.existsByUsernameIncludingDeleted("jmarin3")).thenReturn(true);
        when(userRepository.existsByUsernameIncludingDeleted("jmarin4")).thenReturn(false);

        // When
        String result = usernameGenerator.generateUsername(firstName, lastName, userRepository);

        // Then
        assertThat(result).isEqualTo("jmarin4");
    }

    @Test
    @DisplayName("Should handle single character first name")
    void shouldHandleSingleCharacterFirstName() {
        // Given
        String firstName = "A";
        String lastName = "Smith";
        when(userRepository.existsByUsernameIncludingDeleted("asmith")).thenReturn(false);

        // When
        String result = usernameGenerator.generateUsername(firstName, lastName, userRepository);

        // Then
        assertThat(result).isEqualTo("asmith");
    }

    @Test
    @DisplayName("Should convert to lowercase")
    void shouldConvertToLowercase() {
        // Given
        String firstName = "JOSE";
        String lastName = "MARIN";
        when(userRepository.existsByUsernameIncludingDeleted("jmarin")).thenReturn(false);

        // When
        String result = usernameGenerator.generateUsername(firstName, lastName, userRepository);

        // Then
        assertThat(result).isEqualTo("jmarin");
    }

    @Test
    @DisplayName("Should handle names with only special characters")
    void shouldHandleNamesWithOnlySpecialCharacters() {
        // Given
        String firstName = "José-María";
        String lastName = "O'Connor";
        when(userRepository.existsByUsernameIncludingDeleted("joconnor")).thenReturn(false);

        // When
        String result = usernameGenerator.generateUsername(firstName, lastName, userRepository);

        // Then
        assertThat(result).isEqualTo("joconnor");
    }

    @Test
    @DisplayName("Should throw exception when firstName is null")
    void shouldThrowExceptionWhenFirstNameIsNull() {
        // Given
        String firstName = null;
        String lastName = "Marin";

        // When & Then
        assertThatThrownBy(() -> usernameGenerator.generateUsername(firstName, lastName, userRepository))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("First name cannot be null or empty");
    }

    @Test
    @DisplayName("Should throw exception when firstName is empty")
    void shouldThrowExceptionWhenFirstNameIsEmpty() {
        // Given
        String firstName = "   ";
        String lastName = "Marin";

        // When & Then
        assertThatThrownBy(() -> usernameGenerator.generateUsername(firstName, lastName, userRepository))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("First name cannot be null or empty");
    }

    @Test
    @DisplayName("Should throw exception when lastName is null")
    void shouldThrowExceptionWhenLastNameIsNull() {
        // Given
        String firstName = "Jose";
        String lastName = null;

        // When & Then
        assertThatThrownBy(() -> usernameGenerator.generateUsername(firstName, lastName, userRepository))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Last name cannot be null or empty");
    }

    @Test
    @DisplayName("Should throw exception when lastName is empty")
    void shouldThrowExceptionWhenLastNameIsEmpty() {
        // Given
        String firstName = "Jose";
        String lastName = "";

        // When & Then
        assertThatThrownBy(() -> usernameGenerator.generateUsername(firstName, lastName, userRepository))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Last name cannot be null or empty");
    }

    @Test
    @DisplayName("Should handle names with numbers")
    void shouldHandleNamesWithNumbers() {
        // Given
        String firstName = "Jose2";
        String lastName = "Marin3";
        when(userRepository.existsByUsernameIncludingDeleted("jmarin3")).thenReturn(false);

        // When
        String result = usernameGenerator.generateUsername(firstName, lastName, userRepository);

        // Then
        assertThat(result).isEqualTo("jmarin3");
    }
}
